import { Suspense } from "react";
import { useRoutes, Routes, Route } from "react-router-dom";
import Home from "./components/home";
import Login from "./components/Login";
import { AuthProvider, useAuth } from "./contexts/AuthContext";
import routes from "tempo-routes";

function AppContent() {
  const { isLoggedIn } = useAuth();

  if (!isLoggedIn) {
    return <Login onLogin={() => {}} language="arabic" />;
  }

  return (
    <Suspense fallback={<p>Loading...</p>}>
      <>
        <Routes>
          <Route path="/" element={<Home />} />
        </Routes>
        {import.meta.env.VITE_TEMPO === "true" && useRoutes(routes)}
      </>
    </Suspense>
  );
}

function App() {
  return (
    <AuthProvider>
      <AppContent />
    </AuthProvider>
  );
}

export default App;
