import React, { useState, useEffect } from "react";

function App() {
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Check if user is already logged in
    const savedLoginStatus = localStorage.getItem("isLoggedIn") === "true";
    setIsLoggedIn(savedLoginStatus);
    setLoading(false);
  }, []);

  const handleLogin = async (username: string, password: string): Promise<boolean> => {
    // Simple authentication
    if ((username === "admin" && password === "1-1") ||
        (username === "cashier" && password === "2-2")) {
      setIsLoggedIn(true);
      localStorage.setItem("isLoggedIn", "true");
      localStorage.setItem("userType", username === "admin" ? "admin" : "cashier");
      return true;
    }
    return false;
  };

  const handleLogout = () => {
    setIsLoggedIn(false);
    localStorage.removeItem("isLoggedIn");
    localStorage.removeItem("userType");
  };

  if (loading) {
    return (
      <div style={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        minHeight: '100vh',
        fontFamily: 'Arial, sans-serif'
      }}>
        <div style={{ fontSize: '18px' }}>جاري التحميل...</div>
      </div>
    );
  }

  if (!isLoggedIn) {
    return (
      <div style={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        minHeight: '100vh',
        fontFamily: 'Arial, sans-serif',
        backgroundColor: '#f5f5f5'
      }}>
        <div style={{
          backgroundColor: 'white',
          padding: '40px',
          borderRadius: '8px',
          boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
          width: '400px',
          textAlign: 'center'
        }}>
          <h2 style={{ marginBottom: '30px', color: '#333' }}>تسجيل دخول نظام المطعم</h2>

          <form onSubmit={(e) => {
            e.preventDefault();
            const formData = new FormData(e.target as HTMLFormElement);
            const username = formData.get('username') as string;
            const password = formData.get('password') as string;
            handleLogin(username, password);
          }}>
            <div style={{ marginBottom: '20px', textAlign: 'right' }}>
              <label style={{ display: 'block', marginBottom: '5px', fontWeight: 'bold' }}>
                اسم المستخدم:
              </label>
              <input
                name="username"
                type="text"
                style={{
                  width: '100%',
                  padding: '10px',
                  border: '1px solid #ddd',
                  borderRadius: '4px',
                  fontSize: '16px'
                }}
                placeholder="أدخل اسم المستخدم"
                required
              />
            </div>

            <div style={{ marginBottom: '20px', textAlign: 'right' }}>
              <label style={{ display: 'block', marginBottom: '5px', fontWeight: 'bold' }}>
                كلمة المرور:
              </label>
              <input
                name="password"
                type="password"
                style={{
                  width: '100%',
                  padding: '10px',
                  border: '1px solid #ddd',
                  borderRadius: '4px',
                  fontSize: '16px'
                }}
                placeholder="أدخل كلمة المرور"
                required
              />
            </div>

            <button
              type="submit"
              style={{
                width: '100%',
                padding: '12px',
                backgroundColor: '#007bff',
                color: 'white',
                border: 'none',
                borderRadius: '4px',
                fontSize: '16px',
                cursor: 'pointer'
              }}
            >
              تسجيل الدخول
            </button>
          </form>

          <div style={{ marginTop: '20px', fontSize: '14px', color: '#666' }}>
            <div>المدير: admin / 1-1</div>
            <div>الكاشير: cashier / 2-2</div>
          </div>
        </div>
      </div>
    );
  }

  const userType = localStorage.getItem("userType") || "cashier";

  return (
    <div style={{
      fontFamily: 'Arial, sans-serif',
      padding: '20px',
      minHeight: '100vh',
      backgroundColor: '#f5f5f5'
    }}>
      <div style={{
        backgroundColor: 'white',
        padding: '20px',
        borderRadius: '8px',
        boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)'
      }}>
        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginBottom: '30px',
          borderBottom: '1px solid #eee',
          paddingBottom: '20px'
        }}>
          <h1 style={{ margin: 0, color: '#333' }}>نظام إدارة المطعم</h1>
          <div style={{ display: 'flex', alignItems: 'center', gap: '20px' }}>
            <span style={{ color: '#666' }}>
              {userType === "admin" ? "مدير النظام" : "الكاشير"}
            </span>
            <button
              onClick={handleLogout}
              style={{
                padding: '8px 16px',
                backgroundColor: '#dc3545',
                color: 'white',
                border: 'none',
                borderRadius: '4px',
                cursor: 'pointer'
              }}
            >
              تسجيل خروج
            </button>
          </div>
        </div>

        <div style={{ textAlign: 'center', padding: '40px' }}>
          <h2 style={{ color: '#28a745', marginBottom: '20px' }}>✅ تم تسجيل الدخول بنجاح!</h2>

          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
            gap: '20px',
            marginTop: '30px'
          }}>
            <div style={{
              backgroundColor: '#e3f2fd',
              padding: '20px',
              borderRadius: '8px',
              border: '1px solid #2196f3'
            }}>
              <h3 style={{ color: '#1976d2', marginBottom: '10px' }}>📋 إدارة الطلبات</h3>
              <p style={{ color: '#666', margin: 0 }}>إنشاء ومتابعة الطلبات</p>
            </div>

            {userType === "admin" && (
              <>
                <div style={{
                  backgroundColor: '#f3e5f5',
                  padding: '20px',
                  borderRadius: '8px',
                  border: '1px solid #9c27b0'
                }}>
                  <h3 style={{ color: '#7b1fa2', marginBottom: '10px' }}>📊 التقارير</h3>
                  <p style={{ color: '#666', margin: 0 }}>تقارير المبيعات والإحصائيات</p>
                </div>

                <div style={{
                  backgroundColor: '#e8f5e8',
                  padding: '20px',
                  borderRadius: '8px',
                  border: '1px solid #4caf50'
                }}>
                  <h3 style={{ color: '#388e3c', marginBottom: '10px' }}>🍽️ إدارة المخزون</h3>
                  <p style={{ color: '#666', margin: 0 }}>إدارة المنتجات والقائمة</p>
                </div>
              </>
            )}
          </div>

          <div style={{
            marginTop: '40px',
            padding: '20px',
            backgroundColor: '#fff3cd',
            borderRadius: '8px',
            border: '1px solid #ffc107'
          }}>
            <h3 style={{ color: '#856404', marginBottom: '15px' }}>🎯 المميزات المتاحة:</h3>
            <ul style={{
              textAlign: 'right',
              color: '#666',
              listStyle: 'none',
              padding: 0
            }}>
              <li style={{ marginBottom: '8px' }}>✅ 16 منتج عربي متنوع</li>
              <li style={{ marginBottom: '8px' }}>✅ نظام طلبات كامل</li>
              <li style={{ marginBottom: '8px' }}>✅ حساب الضريبة تلقائياً</li>
              <li style={{ marginBottom: '8px' }}>✅ كلمات مرور بسيطة (1-1 و 2-2)</li>
              {userType === "admin" && (
                <>
                  <li style={{ marginBottom: '8px' }}>✅ تقارير مفصلة</li>
                  <li style={{ marginBottom: '8px' }}>✅ إدارة المخزون</li>
                </>
              )}
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}

export default App;
