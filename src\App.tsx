import React, { useState, useEffect } from "react";
import Home from "./components/home";
import Login from "./components/Login";

function App() {
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Check if user is already logged in
    const savedLoginStatus = localStorage.getItem("isLoggedIn") === "true";
    setIsLoggedIn(savedLoginStatus);
    setLoading(false);
  }, []);

  const handleLogin = async (username: string, password: string): Promise<boolean> => {
    // Simple authentication
    if ((username === "1" && password === "1") ||
        (username === "2" && password === "2")) {
      setIsLoggedIn(true);
      localStorage.setItem("isLoggedIn", "true");
      localStorage.setItem("userType", username === "1" ? "admin" : "cashier");
      return true;
    }
    return false;
  };

  const handleLogout = () => {
    setIsLoggedIn(false);
    localStorage.removeItem("isLoggedIn");
    localStorage.removeItem("userType");
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-lg">جاري التحميل...</div>
      </div>
    );
  }

  if (!isLoggedIn) {
    return <Login onLogin={handleLogin} language="arabic" />;
  }

  return <Home onLogout={handleLogout} />;
}

export default App;
