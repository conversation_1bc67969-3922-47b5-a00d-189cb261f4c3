@import url("https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Amiri:ital,wght@0,400;0,700;1,400;1,700&display=swap");

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Calm Gray Theme - Professional Restaurant Colors */
    --background: 220 15% 97%;
    --foreground: 220 15% 15%;

    --card: 220 15% 99%;
    --card-foreground: 220 15% 15%;

    --popover: 220 15% 99%;
    --popover-foreground: 220 15% 15%;

    --primary: 220 15% 25%;
    --primary-foreground: 220 15% 98%;

    --secondary: 220 15% 92%;
    --secondary-foreground: 220 15% 25%;

    --muted: 220 15% 94%;
    --muted-foreground: 220 10% 45%;

    --accent: 220 15% 90%;
    --accent-foreground: 220 15% 25%;

    --destructive: 0 65% 55%;
    --destructive-foreground: 220 15% 98%;

    --success: 142 70% 45%;
    --success-foreground: 220 15% 98%;

    --warning: 38 90% 55%;
    --warning-foreground: 220 15% 15%;

    --info: 210 90% 55%;
    --info-foreground: 220 15% 98%;

    --border: 220 15% 88%;
    --input: 220 15% 95%;
    --ring: 220 15% 35%;

    --radius: 0.75rem;
  }

  .dark {
    /* Dark mode with warm grays */
    --background: 220 15% 8%;
    --foreground: 220 15% 92%;

    --card: 220 15% 10%;
    --card-foreground: 220 15% 92%;

    --popover: 220 15% 10%;
    --popover-foreground: 220 15% 92%;

    --primary: 220 15% 85%;
    --primary-foreground: 220 15% 15%;

    --secondary: 220 15% 15%;
    --secondary-foreground: 220 15% 85%;

    --muted: 220 15% 12%;
    --muted-foreground: 220 10% 60%;

    --accent: 220 15% 18%;
    --accent-foreground: 220 15% 85%;

    --destructive: 0 65% 55%;
    --destructive-foreground: 220 15% 92%;

    --success: 142 70% 45%;
    --success-foreground: 220 15% 92%;

    --warning: 38 90% 55%;
    --warning-foreground: 220 15% 15%;

    --info: 210 90% 55%;
    --info-foreground: 220 15% 92%;

    --border: 220 15% 18%;
    --input: 220 15% 15%;
    --ring: 220 15% 70%;
  }
}

@layer base {
  * {
    @apply border-border;
    transition: background-color 0.2s ease, border-color 0.2s ease, color 0.2s ease;
  }

  body {
    @apply bg-background text-foreground font-arabic;
    font-feature-settings: "rlig" 1, "calt" 1;
  }

  /* Custom scrollbar */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  ::-webkit-scrollbar-track {
    @apply bg-muted;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-muted-foreground/30 rounded-full;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-muted-foreground/50;
  }

  /* Focus styles */
  .focus-visible {
    @apply outline-none ring-2 ring-ring ring-offset-2 ring-offset-background;
  }
}

@layer components {
  .font-arabic {
    font-family:
      "Cairo",
      "Amiri",
      "Noto Sans Arabic",
      -apple-system,
      BlinkMacSystemFont,
      "Segoe UI",
      Roboto,
      sans-serif;
    font-weight: 400;
    line-height: 1.6;
  }

  .font-arabic-bold {
    font-family: "Cairo", "Amiri", "Noto Sans Arabic", sans-serif;
    font-weight: 600;
  }

  .font-arabic-heading {
    font-family: "Cairo", "Amiri", sans-serif;
    font-weight: 700;
    line-height: 1.4;
  }
}

@layer utilities {
  .rtl {
    direction: rtl;
  }

  .ltr {
    direction: ltr;
  }

  /* RTL-specific spacing utilities */
  .rtl\:space-x-reverse > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 1;
  }

  .rtl\:ml-0 {
    margin-left: 0;
  }

  .rtl\:mr-2 {
    margin-right: 0.5rem;
  }

  .rtl\:mr-0 {
    margin-right: 0;
  }

  .rtl\:ml-1 {
    margin-left: 0.25rem;
  }

  .rtl\:ml-2 {
    margin-left: 0.5rem;
  }

  .rtl\:right-auto {
    right: auto;
  }

  .rtl\:left-4 {
    left: 1rem;
  }

  /* Status colors */
  .status-pending {
    @apply bg-warning/10 text-warning-foreground border-warning/20;
  }

  .status-preparing {
    @apply bg-info/10 text-info-foreground border-info/20;
  }

  .status-ready {
    @apply bg-success/10 text-success-foreground border-success/20;
  }

  .status-delivered {
    @apply bg-muted text-muted-foreground border-muted;
  }

  .status-cancelled {
    @apply bg-destructive/10 text-destructive-foreground border-destructive/20;
  }

  /* Order type colors */
  .order-dine-in {
    @apply bg-green-50 text-green-700 border-green-200;
  }

  .order-delivery {
    @apply bg-blue-50 text-blue-700 border-blue-200;
  }

  .order-takeaway {
    @apply bg-orange-50 text-orange-700 border-orange-200;
  }

  /* Payment method colors */
  .payment-cash {
    @apply bg-gray-50 text-gray-700 border-gray-200;
  }

  .payment-card {
    @apply bg-purple-50 text-purple-700 border-purple-200;
  }

  .payment-online {
    @apply bg-cyan-50 text-cyan-700 border-cyan-200;
  }

  /* Dark mode adjustments */
  .dark .order-dine-in {
    @apply bg-green-900/20 text-green-300 border-green-800/30;
  }

  .dark .order-delivery {
    @apply bg-blue-900/20 text-blue-300 border-blue-800/30;
  }

  .dark .order-takeaway {
    @apply bg-orange-900/20 text-orange-300 border-orange-800/30;
  }

  .dark .payment-cash {
    @apply bg-gray-800/20 text-gray-300 border-gray-700/30;
  }

  .dark .payment-card {
    @apply bg-purple-900/20 text-purple-300 border-purple-800/30;
  }

  .dark .payment-online {
    @apply bg-cyan-900/20 text-cyan-300 border-cyan-800/30;
  }

  /* Animation classes */
  .fade-in {
    animation: fadeIn 0.3s ease-in-out;
  }

  .slide-up {
    animation: slideUp 0.3s ease-out;
  }

  @keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
  }

  @keyframes slideUp {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
  }

  /* Custom gray theme styles */
  .gray-theme {
    --background: #f8f9fa;
    --foreground: #343a40;
    --card: #ffffff;
    --card-foreground: #343a40;
    --primary: #6c757d;
    --primary-foreground: #ffffff;
    --secondary: #e9ecef;
    --secondary-foreground: #495057;
    --muted: #f8f9fa;
    --muted-foreground: #6c757d;
    --accent: #e9ecef;
    --accent-foreground: #343a40;
    --border: #dee2e6;
    --input: #ffffff;
    --ring: #6c757d;
  }

  /* Tab styling improvements */
  [data-state="active"] {
    background-color: #6c757d !important;
    color: white !important;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  }

  /* Button hover effects */
  .hover\\:opacity-90:hover {
    opacity: 0.9;
    transform: translateY(-1px);
    transition: all 0.2s ease;
  }

  /* Smooth transitions */
  * {
    transition: background-color 0.2s ease, color 0.2s ease, border-color 0.2s ease;
  }
}
