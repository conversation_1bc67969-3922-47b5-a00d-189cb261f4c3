import React, { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Content } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { Globe, Moon, Sun, Download, LogOut } from "lucide-react";
import OrderManagement from "./OrderManagement";
import ReportsDashboard from "./ReportsDashboard";
import ProductCatalog from "./ProductCatalog";

interface HomeProps {
  onLogout: () => void;
}

const Home = ({ onLogout }: HomeProps) => {
  const userType = localStorage.getItem("userType") || "cashier";
  const [language, setLanguage] = useState<"english" | "arabic">("arabic");
  const [theme, setTheme] = useState<"light" | "dark">("light");

  const toggleLanguage = () => {
    setLanguage(language === "english" ? "arabic" : "english");
  };

  const toggleTheme = () => {
    setTheme(theme === "light" ? "dark" : "light");
    document.documentElement.classList.toggle("dark");
  };

  const handleDownloadProject = () => {
    // Create detailed download instructions
    const instructions = `
طرق تحميل المشروع:

📁 الطريقة الأولى - تحميل مباشر:
1. اذهب إلى منصة Tempo
2. اضغط على زر "Share" في الأعلى
3. اختر "Deploy" لنشر المشروع
4. أو اضغط على "Export Code" لتحميل الملفات

💻 الطريقة الثانية - Git:
1. إذا كان لديك Git مثبت:
   git clone [رابط المستودع]
2. ثم:
   cd [اسم المجلد]
   npm install
   npm run dev

📋 الطريقة الثالثة - نسخ الملفات:
1. انسخ جميع الملفات من المشروع
2. أنشئ مجلد جديد على جهازك
3. الصق الملفات
4. افتح Terminal في المجلد
5. اكتب: npm install
6. ثم: npm run dev

🔧 متطلبات التشغيل:
- Node.js (الإصدار 16 أو أحدث)
- npm أو yarn
- محرر نصوص (VS Code مُوصى به)

📱 للنشر على الإنترنت:
- استخدم Vercel أو Netlify
- أو GitHub Pages
- أو أي خدمة استضافة أخرى
    `;

    alert(instructions);

    // Also log to console for easy copying
    console.log("تعليمات تحميل المشروع:", instructions);
  };

  useEffect(() => {
    // Set RTL direction for Arabic
    if (language === "arabic") {
      document.documentElement.dir = "rtl";
      document.documentElement.lang = "ar";
    } else {
      document.documentElement.dir = "ltr";
      document.documentElement.lang = "en";
    }
  }, [language]);

  return (
    <div
      className={`min-h-screen flex flex-col ${language === "arabic" ? "font-arabic" : ""}`}
      style={{ backgroundColor: '#f8f9fa' }}
    >
      {/* Header */}
      <header
        className="border-b p-6 flex justify-between items-center"
        style={{
          backgroundColor: '#e9ecef',
          borderColor: '#dee2e6',
          boxShadow: '0 2px 8px rgba(0,0,0,0.08)'
        }}
      >
        <div className="flex items-center space-x-4 rtl:space-x-reverse">
          <div className="flex items-center space-x-3 rtl:space-x-reverse">
            <div
              className="w-12 h-12 rounded-full flex items-center justify-center"
              style={{
                backgroundColor: '#6c757d',
                color: 'white'
              }}
            >
              <span className="font-bold text-xl">م</span>
            </div>
            <div>
              <h1 className="text-3xl font-bold" style={{ color: '#343a40' }}>
                نظام نقاط البيع للمطعم
              </h1>
              <p className="text-sm" style={{ color: '#6c757d' }}>
                نظام إدارة شامل للمطاعم
              </p>
            </div>
          </div>
        </div>

        <div className="flex items-center space-x-3 rtl:space-x-reverse">
          <div className="text-right mr-4">
            <div
              className="text-lg font-semibold"
              style={{ color: '#495057' }}
            >
              {userType === "admin" ? "مدير النظام" : "الكاشير"}
            </div>
            <div className="text-sm" style={{ color: '#6c757d' }}>
              {new Date().toLocaleDateString('ar-SA', {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric'
              })}
            </div>
          </div>

          <Button
            variant="outline"
            size="sm"
            onClick={handleDownloadProject}
            style={{
              backgroundColor: '#6c757d',
              borderColor: '#6c757d',
              color: 'white'
            }}
            className="hover:opacity-90"
          >
            <Download className="h-4 w-4 ml-2 rtl:ml-0 rtl:mr-2" />
            تحميل المشروع
          </Button>

          <Button
            variant="outline"
            size="icon"
            onClick={toggleLanguage}
            style={{
              backgroundColor: '#6c757d',
              borderColor: '#6c757d',
              color: 'white'
            }}
            className="hover:opacity-90"
          >
            <Globe className="h-5 w-5" />
          </Button>

          <Button
            variant="outline"
            size="icon"
            onClick={toggleTheme}
            style={{
              backgroundColor: '#6c757d',
              borderColor: '#6c757d',
              color: 'white'
            }}
            className="hover:opacity-90"
          >
            {theme === "light" ? (
              <Moon className="h-5 w-5" />
            ) : (
              <Sun className="h-5 w-5" />
            )}
          </Button>

          <Button
            variant="outline"
            size="icon"
            onClick={onLogout}
            style={{
              backgroundColor: '#dc3545',
              borderColor: '#dc3545',
              color: 'white'
            }}
            className="hover:opacity-90"
          >
            <LogOut className="h-5 w-5" />
          </Button>
        </div>
      </header>

      {/* Main Content */}
      <main className="flex-1 p-6" style={{ backgroundColor: '#f8f9fa' }}>
        <Tabs defaultValue="orders" className="w-full">
          <TabsList
            className="mb-6 p-1"
            style={{
              backgroundColor: '#e9ecef',
              borderRadius: '12px',
              boxShadow: '0 2px 4px rgba(0,0,0,0.05)'
            }}
          >
            <TabsTrigger
              value="orders"
              className="px-6 py-3 text-lg font-medium rounded-lg transition-all"
              style={{
                color: '#495057'
              }}
            >
              📋 إدارة الطلبات
            </TabsTrigger>
            {userType === "admin" && (
              <TabsTrigger
                value="inventory"
                className="px-6 py-3 text-lg font-medium rounded-lg transition-all"
                style={{
                  color: '#495057'
                }}
              >
                🍽️ إدارة المخزون
              </TabsTrigger>
            )}
            {userType === "admin" && (
              <TabsTrigger
                value="reports"
                className="px-6 py-3 text-lg font-medium rounded-lg transition-all"
                style={{
                  color: '#495057'
                }}
              >
                📊 تقارير المبيعات
              </TabsTrigger>
            )}
          </TabsList>

          <TabsContent
            value="orders"
            className="space-y-4"
            style={{
              backgroundColor: 'white',
              borderRadius: '12px',
              padding: '24px',
              boxShadow: '0 4px 6px rgba(0,0,0,0.05)',
              border: '1px solid #dee2e6'
            }}
          >
            <OrderManagement language={language} userType={userType as "admin" | "cashier"} />
          </TabsContent>

          {userType === "admin" && (
            <TabsContent
              value="inventory"
              className="space-y-4"
              style={{
                backgroundColor: 'white',
                borderRadius: '12px',
                padding: '24px',
                boxShadow: '0 4px 6px rgba(0,0,0,0.05)',
                border: '1px solid #dee2e6'
              }}
            >
              <ProductCatalog
                showInventoryControls={true}
                language={language}
              />
            </TabsContent>
          )}

          {userType === "admin" && (
            <TabsContent
              value="reports"
              className="space-y-4"
              style={{
                backgroundColor: 'white',
                borderRadius: '12px',
                padding: '24px',
                boxShadow: '0 4px 6px rgba(0,0,0,0.05)',
                border: '1px solid #dee2e6'
              }}
            >
              <ReportsDashboard language={language} />
            </TabsContent>
          )}
        </Tabs>
      </main>

      {/* Footer */}
      <footer
        className="border-t p-4 text-center"
        style={{
          backgroundColor: '#e9ecef',
          borderColor: '#dee2e6',
          color: '#6c757d'
        }}
      >
        <div className="flex justify-center items-center space-x-2 rtl:space-x-reverse">
          <span className="text-sm font-medium">
            © 2024 نظام نقاط البيع للمطعم. جميع الحقوق محفوظة.
          </span>
          <span className="text-lg">🍽️</span>
        </div>
      </footer>
    </div>
  );
};

export default Home;
