import React, { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>ger, Ta<PERSON>Content } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { Globe, Moon, Sun, Download, LogOut } from "lucide-react";
import { useAuth } from "@/contexts/AuthContext";
import OrderManagement from "./OrderManagement";
import ReportsDashboard from "./ReportsDashboard";
import ProductCatalog from "./ProductCatalog";

const Home = () => {
  const { userType, logout } = useAuth();
  const [language, setLanguage] = useState<"english" | "arabic">("arabic");
  const [theme, setTheme] = useState<"light" | "dark">("light");

  const toggleLanguage = () => {
    setLanguage(language === "english" ? "arabic" : "english");
  };

  const toggleTheme = () => {
    setTheme(theme === "light" ? "dark" : "light");
    document.documentElement.classList.toggle("dark");
  };

  const handleDownloadProject = () => {
    // Create detailed download instructions
    const instructions = `
طرق تحميل المشروع:

📁 الطريقة الأولى - تحميل مباشر:
1. اذهب إلى منصة Tempo
2. اضغط على زر "Share" في الأعلى
3. اختر "Deploy" لنشر المشروع
4. أو اضغط على "Export Code" لتحميل الملفات

💻 الطريقة الثانية - Git:
1. إذا كان لديك Git مثبت:
   git clone [رابط المستودع]
2. ثم:
   cd [اسم المجلد]
   npm install
   npm run dev

📋 الطريقة الثالثة - نسخ الملفات:
1. انسخ جميع الملفات من المشروع
2. أنشئ مجلد جديد على جهازك
3. الصق الملفات
4. افتح Terminal في المجلد
5. اكتب: npm install
6. ثم: npm run dev

🔧 متطلبات التشغيل:
- Node.js (الإصدار 16 أو أحدث)
- npm أو yarn
- محرر نصوص (VS Code مُوصى به)

📱 للنشر على الإنترنت:
- استخدم Vercel أو Netlify
- أو GitHub Pages
- أو أي خدمة استضافة أخرى
    `;

    alert(instructions);

    // Also log to console for easy copying
    console.log("تعليمات تحميل المشروع:", instructions);
  };

  useEffect(() => {
    // Set RTL direction for Arabic
    if (language === "arabic") {
      document.documentElement.dir = "rtl";
      document.documentElement.lang = "ar";
    } else {
      document.documentElement.dir = "ltr";
      document.documentElement.lang = "en";
    }
  }, [language]);

  return (
    <div
      className={`min-h-screen bg-background flex flex-col ${language === "arabic" ? "font-arabic" : ""}`}
    >
      {/* Header */}
      <header className="border-b bg-card p-4 flex justify-between items-center">
        <div className="flex items-center space-x-4 rtl:space-x-reverse">
          <h1 className="text-2xl font-bold">نظام نقاط البيع للمطعم</h1>
        </div>
        <div className="flex items-center space-x-2 rtl:space-x-reverse">
          <div className="text-sm text-muted-foreground mr-4">
            {userType === "admin" ? "مدير" : "كاشير"}
          </div>
          <Button variant="outline" size="sm" onClick={handleDownloadProject}>
            <Download className="h-4 w-4 ml-2 rtl:ml-0 rtl:mr-2" />
            تحميل المشروع
          </Button>
          <Button variant="outline" size="icon" onClick={toggleLanguage}>
            <Globe className="h-5 w-5" />
          </Button>
          <Button variant="outline" size="icon" onClick={toggleTheme}>
            {theme === "light" ? (
              <Moon className="h-5 w-5" />
            ) : (
              <Sun className="h-5 w-5" />
            )}
          </Button>
          <Button variant="outline" size="icon" onClick={logout}>
            <LogOut className="h-5 w-5" />
          </Button>
        </div>
      </header>

      {/* Main Content */}
      <main className="flex-1 p-4">
        <Tabs defaultValue="orders" className="w-full">
          <TabsList className="mb-4">
            <TabsTrigger value="orders">إدارة الطلبات</TabsTrigger>
            {userType === "admin" && (
              <TabsTrigger value="inventory">إدارة المخزون</TabsTrigger>
            )}
            {userType === "admin" && (
              <TabsTrigger value="reports">تقارير المبيعات</TabsTrigger>
            )}
          </TabsList>

          <TabsContent value="orders" className="space-y-4">
            <OrderManagement language={language} userType={userType} />
          </TabsContent>

          {userType === "admin" && (
            <TabsContent value="inventory" className="space-y-4">
              <ProductCatalog
                showInventoryControls={true}
                language={language}
              />
            </TabsContent>
          )}

          {userType === "admin" && (
            <TabsContent value="reports" className="space-y-4">
              <ReportsDashboard language={language} />
            </TabsContent>
          )}
        </Tabs>
      </main>

      {/* Footer */}
      <footer className="border-t bg-card p-4 text-center text-sm text-muted-foreground">
        © 2024 نظام نقاط البيع للمطعم. جميع الحقوق محفوظة.
      </footer>
    </div>
  );
};

export default Home;
