import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  <PERSON>,
  <PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON>ooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
  Trash2,
  Plus,
  Minus,
  CreditCard,
  Banknote,
  Tag,
  Printer,
} from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface OrderItem {
  id: string;
  name: string;
  price: number;
  quantity: number;
  notes?: string;
}

interface CustomerInfo {
  name: string;
  phone: string;
  address?: string;
}

interface OrderDetailsProps {
  orderType?: "dine-in" | "delivery" | "takeaway";
  tableNumber?: number;
  orderStatus?: "new" | "in-progress" | "completed";
  customerInfo?: CustomerInfo;
  items?: OrderItem[];
  onUpdateStatus?: (status: "new" | "in-progress" | "completed") => void;
  onProcessPayment?: (method: "cash") => void;
  onUpdateQuantity?: (itemId: string, quantity: number) => void;
  onRemoveItem?: (itemId: string) => void;
  onApplyDiscount?: (discountPercent: number) => void;
  onPrintOrder?: () => void;
  language?: "english" | "arabic";
}

const OrderDetails = ({
  orderType = "dine-in",
  tableNumber = 1,
  orderStatus = "new",
  customerInfo = { name: "", phone: "" },
  items = [
    { id: "1", name: "برجر دجاج", price: 8.99, quantity: 2 },
    { id: "2", name: "بطاطس مقلية", price: 3.5, quantity: 1 },
    { id: "3", name: "كولا", price: 1.99, quantity: 2 },
  ],
  onUpdateStatus = () => {},
  onProcessPayment = () => {},
  onUpdateQuantity = () => {},
  onRemoveItem = () => {},
  onApplyDiscount = () => {},
  onPrintOrder = () => {},
  language = "arabic",
}: OrderDetailsProps) => {
  const [discount, setDiscount] = useState(0);
  const [notes, setNotes] = useState("");

  // Calculate totals
  const subtotal = items.reduce(
    (sum, item) => sum + item.price * item.quantity,
    0,
  );
  const discountAmount = subtotal * (discount / 100);
  const total = subtotal - discountAmount;

  const getStatusColor = (status: string) => {
    switch (status) {
      case "new":
        return "bg-blue-500";
      case "in-progress":
        return "bg-yellow-500";
      case "completed":
        return "bg-green-500";
      default:
        return "bg-gray-500";
    }
  };

  const handleApplyDiscount = () => {
    onApplyDiscount(discount);
  };

  return (
    <Card className="w-full h-full bg-white shadow-md">
      <CardHeader className="pb-2">
        <div className="flex justify-between items-center">
          <CardTitle className="text-xl font-bold">
            {language === "english" ? "Order Details" : "تفاصيل الطلب"}
          </CardTitle>
          <Badge className={`${getStatusColor(orderStatus)} text-white`}>
            {orderStatus === "new"
              ? language === "english"
                ? "NEW"
                : "جديد"
              : orderStatus === "in-progress"
                ? language === "english"
                  ? "IN PROGRESS"
                  : "قيد التنفيذ"
                : language === "english"
                  ? "COMPLETED"
                  : "مكتمل"}
          </Badge>
        </div>
        <div className="text-sm text-gray-500">
          {orderType === "dine-in" && (
            <span>
              {language === "english"
                ? `Table #${tableNumber}`
                : `طاولة رقم ${tableNumber}`}
            </span>
          )}
          {orderType === "delivery" && (
            <span>
              {language === "english" ? "Delivery Order" : "طلب توصيل"}
            </span>
          )}
          {orderType === "takeaway" && (
            <span>
              {language === "english" ? "Takeaway Order" : "طلب سفري"}
            </span>
          )}
        </div>
      </CardHeader>

      {orderType === "delivery" && (
        <CardContent className="pb-0">
          <div className="grid grid-cols-2 gap-2 mb-4">
            <div>
              <Label htmlFor="customer-name">
                {language === "english" ? "Customer Name" : "اسم العميل"}
              </Label>
              <Input
                id="customer-name"
                value={customerInfo.name}
                placeholder={
                  language === "english"
                    ? "Enter customer name"
                    : "أدخل اسم العميل"
                }
                className="mt-1"
                readOnly
              />
            </div>
            <div>
              <Label htmlFor="customer-phone">
                {language === "english" ? "Phone" : "الهاتف"}
              </Label>
              <Input
                id="customer-phone"
                value={customerInfo.phone}
                placeholder={
                  language === "english"
                    ? "Enter phone number"
                    : "أدخل رقم الهاتف"
                }
                className="mt-1"
                readOnly
              />
            </div>
            <div className="col-span-2">
              <Label htmlFor="customer-address">
                {language === "english" ? "Address" : "العنوان"}
              </Label>
              <Input
                id="customer-address"
                value={customerInfo.address || ""}
                placeholder={
                  language === "english"
                    ? "Enter delivery address"
                    : "أدخل عنوان التوصيل"
                }
                className="mt-1"
                readOnly
              />
            </div>
          </div>
          <Separator className="my-2" />
        </CardContent>
      )}

      <CardContent className="overflow-auto max-h-[400px]">
        <div className="space-y-4">
          {items.length > 0 ? (
            items.map((item) => (
              <div
                key={item.id}
                className="flex items-center justify-between p-2 border rounded-md"
              >
                <div className="flex-1">
                  <div className="font-medium">{item.name}</div>
                  <div className="text-sm text-gray-500">
                    {item.price.toFixed(2)} د.ع
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <Button
                    variant="outline"
                    size="icon"
                    className="h-8 w-8"
                    onClick={() =>
                      onUpdateQuantity(item.id, Math.max(1, item.quantity - 1))
                    }
                  >
                    <Minus className="h-4 w-4" />
                  </Button>
                  <span className="w-8 text-center">{item.quantity}</span>
                  <Button
                    variant="outline"
                    size="icon"
                    className="h-8 w-8"
                    onClick={() => onUpdateQuantity(item.id, item.quantity + 1)}
                  >
                    <Plus className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-8 w-8 text-red-500"
                    onClick={() => onRemoveItem(item.id)}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            ))
          ) : (
            <div className="text-center py-8 text-gray-500">
              {language === "english"
                ? "No items in order"
                : "لا توجد عناصر في الطلب"}
            </div>
          )}
        </div>

        <div className="mt-6">
          <Label htmlFor="order-notes">
            {language === "english" ? "Order Notes" : "ملاحظات الطلب"}
          </Label>
          <textarea
            id="order-notes"
            className="w-full mt-1 p-2 border rounded-md min-h-[80px]"
            placeholder={
              language === "english"
                ? "Add special instructions..."
                : "أضف تعليمات خاصة..."
            }
            value={notes}
            onChange={(e) => setNotes(e.target.value)}
          />
        </div>

        <div className="mt-4 flex items-end gap-2">
          <div className="flex-1">
            <Label htmlFor="discount">
              {language === "english" ? "Discount (%)" : "الخصم (%)"}
            </Label>
            <Input
              id="discount"
              type="number"
              min="0"
              max="100"
              value={discount}
              onChange={(e) => setDiscount(Number(e.target.value))}
              className="mt-1"
            />
          </div>
          <Button
            variant="outline"
            className="flex items-center gap-1"
            onClick={handleApplyDiscount}
          >
            <Tag className="h-4 w-4" />
            {language === "english" ? "Apply" : "تطبيق"}
          </Button>
        </div>
      </CardContent>

      <CardFooter className="flex flex-col pt-4 border-t">
        <div className="w-full space-y-1 mb-4">
          <div className="flex justify-between">
            <span className="text-gray-500">
              {language === "english" ? "Subtotal:" : "المجموع الفرعي:"}
            </span>
            <span>{subtotal.toFixed(2)} د.ع</span>
          </div>
          {discount > 0 && (
            <div className="flex justify-between text-green-600">
              <span>
                {language === "english"
                  ? `Discount (${discount}%):`
                  : `الخصم (${discount}%):`}
              </span>
              <span>-{discountAmount.toFixed(2)} د.ع</span>
            </div>
          )}
          <div className="flex justify-between font-bold text-lg">
            <span>{language === "english" ? "Total:" : "المجموع:"}</span>
            <span>{total.toFixed(2)} د.ع</span>
          </div>
        </div>

        <div className="grid grid-cols-1 gap-2 w-full">
          <div className="flex gap-2">
            <Select
              defaultValue={orderStatus}
              onValueChange={(value) =>
                onUpdateStatus(value as "new" | "in-progress" | "completed")
              }
            >
              <SelectTrigger className="flex-1">
                <SelectValue
                  placeholder={
                    language === "english" ? "Update Status" : "تحديث الحالة"
                  }
                />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="new">
                  {language === "english" ? "New" : "جديد"}
                </SelectItem>
                <SelectItem value="in-progress">
                  {language === "english" ? "In Progress" : "قيد التنفيذ"}
                </SelectItem>
                <SelectItem value="completed">
                  {language === "english" ? "Completed" : "مكتمل"}
                </SelectItem>
              </SelectContent>
            </Select>
            <Button
              variant="outline"
              onClick={onPrintOrder}
              className="flex items-center gap-1"
            >
              <Printer className="h-4 w-4" />
              {language === "english" ? "Print" : "طباعة"}
            </Button>
          </div>

          <Button
            className="w-full flex items-center gap-2 justify-center"
            onClick={() => onProcessPayment("cash")}
            style={{
              backgroundColor: '#28a745',
              borderColor: '#28a745',
              color: 'white'
            }}
          >
            <Banknote className="h-5 w-5" />
            {language === "english" ? "Cash Payment Only" : "دفع نقدي فقط"}
          </Button>
        </div>
      </CardFooter>
    </Card>
  );
};

export default OrderDetails;
