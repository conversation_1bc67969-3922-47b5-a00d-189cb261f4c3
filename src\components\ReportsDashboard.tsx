import React, { useState, useEffect } from "react";
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { format } from "date-fns";
import {
  CalendarIcon,
  Download,
  <PERSON><PERSON>hart,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Printer,
  RefreshCw,
} from "lucide-react";
// import { db, Order } from "@/lib/database";

interface SalesSummary {
  totalSales: number;
  orderCount: number;
  averageOrderValue: number;
}

// Remove the old interface since we're using Order from database

interface ReportsDashboardProps {
  language?: "english" | "arabic";
}

const ReportsDashboard = ({ language = "arabic" }: ReportsDashboardProps) => {
  const [loading, setLoading] = useState(false);
  const [dateFrom, setDateFrom] = useState<Date | undefined>(new Date(new Date().setDate(new Date().getDate() - 7)));
  const [dateTo, setDateTo] = useState<Date | undefined>(new Date());
  const [selectedPeriod, setSelectedPeriod] = useState("week");
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedOrderType, setSelectedOrderType] = useState("all");
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState("all");

  // Enhanced sample data with more orders and dates
  const allSampleOrders = [
    {
      id: "1",
      orderNumber: "ORD001",
      customerName: "أحمد محمد",
      orderType: "dine-in",
      paymentMethod: "cash",
      total: 125.50,
      createdAt: new Date(2024, 11, 10, 14, 30),
      items: [
        { name: "كبسة دجاج", quantity: 2, price: 45 },
        { name: "عصير برتقال", quantity: 2, price: 12 }
      ]
    },
    {
      id: "2",
      orderNumber: "ORD002",
      customerName: "فاطمة علي",
      orderType: "delivery",
      paymentMethod: "cash",
      total: 89.75,
      createdAt: new Date(2024, 11, 10, 15, 45),
      items: [
        { name: "مندي لحم", quantity: 1, price: 65 },
        { name: "شاي أحمر", quantity: 2, price: 8 }
      ]
    },
    {
      id: "3",
      orderNumber: "ORD003",
      customerName: "محمد سالم",
      orderType: "takeaway",
      paymentMethod: "cash",
      total: 67.00,
      createdAt: new Date(2024, 11, 9, 12, 15),
      items: [
        { name: "حمص بالطحينة", quantity: 1, price: 18 },
        { name: "فتوش", quantity: 1, price: 22 },
        { name: "كنافة نابلسية", quantity: 1, price: 25 }
      ]
    },
    {
      id: "4",
      orderNumber: "ORD004",
      customerName: "سارة أحمد",
      orderType: "dine-in",
      paymentMethod: "cash",
      total: 158.00,
      createdAt: new Date(2024, 11, 8, 19, 20),
      items: [
        { name: "مشاوي مشكلة", quantity: 1, price: 85 },
        { name: "تبولة", quantity: 1, price: 20 },
        { name: "قهوة عربية", quantity: 2, price: 10 },
        { name: "بقلاوة", quantity: 1, price: 20 }
      ]
    },
    {
      id: "5",
      orderNumber: "ORD005",
      customerName: "خالد عبدالله",
      orderType: "delivery",
      paymentMethod: "cash",
      total: 76.00,
      createdAt: new Date(2024, 11, 7, 13, 10),
      items: [
        { name: "فراخ مشوية", quantity: 2, price: 38 }
      ]
    },
    {
      id: "6",
      orderNumber: "ORD006",
      customerName: "نورا محمد",
      orderType: "takeaway",
      paymentMethod: "cash",
      total: 47.00,
      createdAt: new Date(2024, 11, 6, 16, 30),
      items: [
        { name: "عصير مانجو", quantity: 2, price: 15 },
        { name: "أم علي", quantity: 1, price: 18 }
      ]
    },
    {
      id: "7",
      orderNumber: "ORD007",
      customerName: "عبدالرحمن يوسف",
      orderType: "dine-in",
      paymentMethod: "cash",
      total: 95.00,
      createdAt: new Date(2024, 11, 5, 20, 45),
      items: [
        { name: "كبسة دجاج", quantity: 1, price: 45 },
        { name: "بابا غنوج", quantity: 1, price: 16 },
        { name: "مهلبية", quantity: 2, price: 15 }
      ]
    }
  ];

  // Filter orders based on date range and other filters (Cash only)
  const filteredOrders = allSampleOrders.filter(order => {
    const orderDate = new Date(order.createdAt);
    const isInDateRange = (!dateFrom || orderDate >= dateFrom) && (!dateTo || orderDate <= dateTo);
    const matchesSearch = order.customerName.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         order.orderNumber.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesOrderType = selectedOrderType === "all" || order.orderType === selectedOrderType;
    // Only cash payments are allowed
    const isCashOnly = order.paymentMethod === "cash";

    return isInDateRange && matchesSearch && matchesOrderType && isCashOnly;
  });

  // Calculate dynamic sales summary based on filtered data
  const salesSummary = {
    totalSales: filteredOrders.reduce((sum, order) => sum + order.total, 0),
    orderCount: filteredOrders.length,
    averageOrderValue: filteredOrders.length > 0 ?
      filteredOrders.reduce((sum, order) => sum + order.total, 0) / filteredOrders.length : 0
  };

  const handlePeriodChange = (period: string) => {
    setSelectedPeriod(period);
    const today = new Date();

    switch (period) {
      case "today":
        setDateFrom(new Date(today.setHours(0, 0, 0, 0)));
        setDateTo(new Date(today.setHours(23, 59, 59, 999)));
        break;
      case "week":
        const weekStart = new Date(today.setDate(today.getDate() - today.getDay()));
        setDateFrom(weekStart);
        setDateTo(new Date());
        break;
      case "month":
        const monthStart = new Date(today.getFullYear(), today.getMonth(), 1);
        setDateFrom(monthStart);
        setDateTo(new Date());
        break;
      case "year":
        const yearStart = new Date(today.getFullYear(), 0, 1);
        setDateFrom(yearStart);
        setDateTo(new Date());
        break;
      case "custom":
        // Keep current dates for custom selection
        break;
    }
  };

  const loadOrders = () => {
    setLoading(true);
    setTimeout(() => {
      setLoading(false);
      alert("تم تحديث البيانات");
    }, 1000);
  };

  const handleExport = (format: "csv" | "pdf" | "excel") => {
    const data = filteredOrders.map(order => ({
      "رقم الطلب": order.orderNumber,
      "العميل": order.customerName,
      "نوع الطلب": order.orderType === "dine-in" ? "تناول في المطعم" :
                   order.orderType === "delivery" ? "توصيل" : "سفري",
      "طريقة الدفع": order.paymentMethod === "cash" ? "نقدي" :
                     order.paymentMethod === "card" ? "بطاقة" : "أونلاين",
      "المبلغ": order.total,
      "التاريخ": format(order.createdAt, "dd/MM/yyyy HH:mm")
    }));

    console.log(`تصدير ${data.length} طلب بصيغة ${format}`, data);
    alert(`تم تصدير ${data.length} طلب بصيغة ${format.toUpperCase()}`);
  };

  const handlePrintReport = () => {
    window.print();
  };

  const clearFilters = () => {
    setSearchQuery("");
    setSelectedOrderType("all");
    setSelectedPaymentMethod("all");
    setSelectedPeriod("week");
    handlePeriodChange("week");
  };

  return (
    <div className="bg-background p-6 w-full h-full">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">📊 تقارير المبيعات المتقدمة</h1>
        <div className="flex gap-2">
          <Button
            onClick={loadOrders}
            variant="outline"
            disabled={loading}
          >
            <RefreshCw className={`mr-2 rtl:mr-0 rtl:ml-2 h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
            تحديث
          </Button>
          <Button
            onClick={handlePrintReport}
            style={{ backgroundColor: '#6c757d', borderColor: '#6c757d' }}
          >
            <Printer className="mr-2 rtl:mr-0 rtl:ml-2 h-4 w-4" />
            طباعة التقرير
          </Button>
          <Button variant="outline" onClick={() => handleExport("csv")}>
            <Download className="mr-2 rtl:mr-0 rtl:ml-2 h-4 w-4" />
            CSV
          </Button>
          <Button variant="outline" onClick={() => handleExport("pdf")}>
            <Download className="mr-2 rtl:mr-0 rtl:ml-2 h-4 w-4" />
            PDF
          </Button>
          <Button variant="outline" onClick={() => handleExport("excel")}>
            <Download className="mr-2 rtl:mr-0 rtl:ml-2 h-4 w-4" />
            Excel
          </Button>
        </div>
      </div>

      {/* Advanced Filters */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle>🔍 البحث والتصفية المتقدمة</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
            {/* Period Selection */}
            <div>
              <label className="block text-sm font-medium mb-2">الفترة الزمنية</label>
              <Select value={selectedPeriod} onValueChange={handlePeriodChange}>
                <SelectTrigger>
                  <SelectValue placeholder="اختر الفترة" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="today">اليوم</SelectItem>
                  <SelectItem value="week">هذا الأسبوع</SelectItem>
                  <SelectItem value="month">هذا الشهر</SelectItem>
                  <SelectItem value="year">هذا العام</SelectItem>
                  <SelectItem value="custom">فترة مخصصة</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Search */}
            <div>
              <label className="block text-sm font-medium mb-2">البحث</label>
              <Input
                placeholder="ابحث بالاسم أو رقم الطلب..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>

            {/* Order Type Filter */}
            <div>
              <label className="block text-sm font-medium mb-2">نوع الطلب</label>
              <Select value={selectedOrderType} onValueChange={setSelectedOrderType}>
                <SelectTrigger>
                  <SelectValue placeholder="جميع الأنواع" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">جميع الأنواع</SelectItem>
                  <SelectItem value="dine-in">تناول في المطعم</SelectItem>
                  <SelectItem value="delivery">توصيل</SelectItem>
                  <SelectItem value="takeaway">سفري</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Time Precision Filter */}
            <div>
              <label className="block text-sm font-medium mb-2">دقة التوقيت</label>
              <Select value="detailed" onValueChange={() => {}}>
                <SelectTrigger>
                  <SelectValue placeholder="تفصيلي" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="detailed">ساعة ودقيقة</SelectItem>
                  <SelectItem value="hourly">بالساعة</SelectItem>
                  <SelectItem value="daily">يومي</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Custom Date Range */}
          {selectedPeriod === "custom" && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
              <div>
                <label className="block text-sm font-medium mb-2">من تاريخ</label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button variant="outline" className="w-full justify-start text-left">
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {dateFrom ? format(dateFrom, "dd/MM/yyyy") : "اختر التاريخ"}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="single"
                      selected={dateFrom}
                      onSelect={setDateFrom}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>
              <div>
                <label className="block text-sm font-medium mb-2">إلى تاريخ</label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button variant="outline" className="w-full justify-start text-left">
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {dateTo ? format(dateTo, "dd/MM/yyyy") : "اختر التاريخ"}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="single"
                      selected={dateTo}
                      onSelect={setDateTo}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>
            </div>
          )}

          <div className="flex gap-2">
            <Button onClick={clearFilters} variant="outline">
              مسح الفلاتر
            </Button>
            <div className="text-sm text-muted-foreground flex items-center">
              عرض {filteredOrders.length} من {allSampleOrders.length} طلب
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Enhanced Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <Card style={{ backgroundColor: '#e3f2fd', borderColor: '#2196f3' }}>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium" style={{ color: '#1976d2' }}>
              💰 إجمالي المبيعات
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold" style={{ color: '#1976d2' }}>
              {salesSummary.totalSales.toFixed(2)} ر.س
            </div>
            <p className="text-xs text-muted-foreground mt-1">
              للفترة المحددة
            </p>
          </CardContent>
        </Card>

        <Card style={{ backgroundColor: '#e8f5e8', borderColor: '#4caf50' }}>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium" style={{ color: '#388e3c' }}>
              📋 عدد الطلبات
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold" style={{ color: '#388e3c' }}>
              {salesSummary.orderCount}
            </div>
            <p className="text-xs text-muted-foreground mt-1">
              طلب مكتمل
            </p>
          </CardContent>
        </Card>

        <Card style={{ backgroundColor: '#fff3e0', borderColor: '#ff9800' }}>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium" style={{ color: '#f57c00' }}>
              📊 متوسط قيمة الطلب
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold" style={{ color: '#f57c00' }}>
              {salesSummary.averageOrderValue.toFixed(2)} ر.س
            </div>
            <p className="text-xs text-muted-foreground mt-1">
              لكل طلب
            </p>
          </CardContent>
        </Card>

        <Card style={{ backgroundColor: '#f3e5f5', borderColor: '#9c27b0' }}>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium" style={{ color: '#7b1fa2' }}>
              🏆 أعلى طلب
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold" style={{ color: '#7b1fa2' }}>
              {filteredOrders.length > 0 ? Math.max(...filteredOrders.map(o => o.total)).toFixed(2) : '0.00'} ر.س
            </div>
            <p className="text-xs text-muted-foreground mt-1">
              أكبر مبلغ
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Enhanced Orders Table */}
      <Card>
        <CardHeader>
          <CardTitle>📋 تفاصيل الطلبات ({filteredOrders.length} طلب)</CardTitle>
        </CardHeader>
        <CardContent>
          {filteredOrders.length > 0 ? (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow style={{ backgroundColor: '#f8f9fa' }}>
                    <TableHead className="font-bold">رقم الطلب</TableHead>
                    <TableHead className="font-bold">العميل</TableHead>
                    <TableHead className="font-bold">التاريخ والوقت المفصل</TableHead>
                    <TableHead className="font-bold">نوع الطلب</TableHead>
                    <TableHead className="font-bold">العناصر</TableHead>
                    <TableHead className="font-bold">المبلغ (نقدي)</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredOrders.map((order) => (
                    <TableRow key={order.id} className="hover:bg-gray-50">
                      <TableCell className="font-medium">{order.orderNumber}</TableCell>
                      <TableCell>{order.customerName}</TableCell>
                      <TableCell className="text-sm">
                        <div className="font-medium">
                          {format(order.createdAt, "dd/MM/yyyy")}
                        </div>
                        <div className="text-xs text-muted-foreground">
                          {format(order.createdAt, "EEEE", { locale: { code: 'ar' } })}
                        </div>
                        <div className="text-sm font-mono" style={{ color: '#495057' }}>
                          {format(order.createdAt, "HH:mm:ss")}
                        </div>
                        <div className="text-xs text-muted-foreground">
                          الساعة {format(order.createdAt, "h")} و {format(order.createdAt, "mm")} دقيقة
                        </div>
                      </TableCell>
                      <TableCell>
                        <span className={`px-2 py-1 rounded text-xs ${
                          order.orderType === "dine-in" ? "bg-green-100 text-green-800" :
                          order.orderType === "delivery" ? "bg-blue-100 text-blue-800" :
                          "bg-orange-100 text-orange-800"
                        }`}>
                          {order.orderType === "dine-in" ? "🍽️ تناول في المطعم" :
                           order.orderType === "delivery" ? "🚚 توصيل" : "📦 سفري"}
                        </span>
                      </TableCell>

                      <TableCell className="text-sm">
                        <div className="max-w-xs">
                          {order.items.slice(0, 2).map((item, index) => (
                            <div key={index} className="text-xs text-muted-foreground">
                              {item.name} ({item.quantity})
                            </div>
                          ))}
                          {order.items.length > 2 && (
                            <div className="text-xs text-muted-foreground">
                              +{order.items.length - 2} عنصر آخر
                            </div>
                          )}
                        </div>
                      </TableCell>
                      <TableCell className="font-bold text-green-600">
                        {order.total.toFixed(2)} ر.س
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          ) : (
            <div className="text-center py-8 text-muted-foreground">
              <div className="text-4xl mb-4">📊</div>
              <h3 className="text-lg font-medium mb-2">لا توجد طلبات</h3>
              <p>لا توجد طلبات تطابق معايير البحث المحددة</p>
              <Button onClick={clearFilters} variant="outline" className="mt-4">
                مسح الفلاتر
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default ReportsDashboard;
