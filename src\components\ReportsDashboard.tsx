import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { format } from "date-fns";
import {
  CalendarIcon,
  Download,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Printer,
} from "lucide-react";

interface SalesSummary {
  totalSales: number;
  orderCount: number;
  averageOrderValue: number;
}

interface SalesRecord {
  id: string;
  date: Date;
  orderType: "dine-in" | "delivery" | "takeaway";
  paymentMethod: "cash" | "card" | "online";
  amount: number;
  items: number;
}

interface ReportsDashboardProps {
  language?: "english" | "arabic";
}

const ReportsDashboard = ({ language = "arabic" }: ReportsDashboardProps) => {
  const [dateRange, setDateRange] = useState<{
    from: Date;
    to?: Date;
  }>({ from: new Date(new Date().setDate(new Date().getDate() - 7)) });

  const [orderTypeFilter, setOrderTypeFilter] = useState<string>("all");
  const [paymentMethodFilter, setPaymentMethodFilter] = useState<string>("all");

  // Mock data for sales summary
  const salesSummary: SalesSummary = {
    totalSales: 12580.5,
    orderCount: 187,
    averageOrderValue: 67.28,
  };

  // Mock data for sales records
  const salesRecords: SalesRecord[] = [
    {
      id: "1001",
      date: new Date(2023, 5, 1),
      orderType: "dine-in",
      paymentMethod: "cash",
      amount: 125.5,
      items: 4,
    },
    {
      id: "1002",
      date: new Date(2023, 5, 1),
      orderType: "delivery",
      paymentMethod: "card",
      amount: 89.75,
      items: 3,
    },
    {
      id: "1003",
      date: new Date(2023, 5, 2),
      orderType: "takeaway",
      paymentMethod: "cash",
      amount: 45.25,
      items: 2,
    },
    {
      id: "1004",
      date: new Date(2023, 5, 2),
      orderType: "dine-in",
      paymentMethod: "online",
      amount: 210.0,
      items: 6,
    },
    {
      id: "1005",
      date: new Date(2023, 5, 3),
      orderType: "delivery",
      paymentMethod: "online",
      amount: 78.5,
      items: 3,
    },
  ];

  const handleExport = (format: "csv" | "pdf" | "excel") => {
    // Placeholder for export functionality
    console.log(`Exporting data in ${format} format`);
  };

  const handlePrintReport = () => {
    const printWindow = window.open("", "_blank");
    if (printWindow) {
      const currentDate = new Date().toLocaleDateString("ar-SA");
      const currentTime = new Date().toLocaleTimeString("ar-SA");

      printWindow.document.write(`
        <html dir="rtl">
          <head>
            <title>تقرير المبيعات</title>
            <style>
              body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; }
              .header { text-align: center; border-bottom: 2px solid #000; padding-bottom: 10px; margin-bottom: 20px; }
              .summary { margin: 20px 0; }
              .summary-card { border: 1px solid #ddd; padding: 15px; margin: 10px 0; border-radius: 5px; }
              .records { margin: 20px 0; }
              table { width: 100%; border-collapse: collapse; margin: 20px 0; }
              th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }
              th { background-color: #f2f2f2; font-weight: bold; }
              .total { border-top: 2px solid #000; padding-top: 10px; font-weight: bold; font-size: 18px; }
            </style>
          </head>
          <body>
            <div class="header">
              <h1>نظام نقاط البيع للمطعم</h1>
              <h2>تقرير المبيعات</h2>
              <p>التاريخ: ${currentDate} | الوقت: ${currentTime}</p>
            </div>
            
            <div class="summary">
              <h3>ملخص المبيعات</h3>
              <div class="summary-card">
                <strong>إجمالي المبيعات:</strong> ${salesSummary.totalSales.toFixed(2)} د.ع
              </div>
              <div class="summary-card">
                <strong>عدد الطلبات:</strong> ${salesSummary.orderCount}
              </div>
              <div class="summary-card">
                <strong>متوسط قيمة الطلب:</strong> ${salesSummary.averageOrderValue.toFixed(2)} د.ع
              </div>
            </div>
            
            <div class="records">
              <h3>سجلات المبيعات التفصيلية</h3>
              <table>
                <thead>
                  <tr>
                    <th>رقم الطلب</th>
                    <th>التاريخ</th>
                    <th>نوع الطلب</th>
                    <th>طريقة الدفع</th>
                    <th>عدد العناصر</th>
                    <th>المبلغ</th>
                  </tr>
                </thead>
                <tbody>
                  ${salesRecords
                    .map(
                      (record) => `
                    <tr>
                      <td>${record.id}</td>
                      <td>${format(record.date, "dd/MM/yyyy")}</td>
                      <td>${record.orderType === "dine-in" ? "تناول في المطعم" : record.orderType === "delivery" ? "توصيل" : "سفري"}</td>
                      <td>${record.paymentMethod === "cash" ? "نقدي" : record.paymentMethod === "card" ? "بطاقة" : "أونلاين"}</td>
                      <td>${record.items}</td>
                      <td>${record.amount.toFixed(2)} د.ع</td>
                    </tr>
                  `,
                    )
                    .join("")}
                </tbody>
              </table>
            </div>
            
            <div class="total">
              <p>إجمالي المبيعات: ${salesSummary.totalSales.toFixed(2)} د.ع</p>
            </div>
            
            <div style="text-align: center; margin-top: 30px;">
              <p>شكراً لاستخدام نظام نقاط البيع</p>
            </div>
          </body>
        </html>
      `);
      printWindow.document.close();
      printWindow.print();
    }
  };

  return (
    <div className="bg-background p-6 w-full h-full">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">تقارير المبيعات</h1>
        <div className="flex gap-2">
          <Button
            onClick={handlePrintReport}
            className="bg-blue-600 hover:bg-blue-700"
          >
            <Printer className="mr-2 rtl:mr-0 rtl:ml-2 h-4 w-4" />
            طباعة التقرير
          </Button>
          <Button variant="outline" onClick={() => handleExport("csv")}>
            <Download className="mr-2 rtl:mr-0 rtl:ml-2 h-4 w-4" />
            CSV
          </Button>
          <Button variant="outline" onClick={() => handleExport("pdf")}>
            <Download className="mr-2 rtl:mr-0 rtl:ml-2 h-4 w-4" />
            PDF
          </Button>
          <Button variant="outline" onClick={() => handleExport("excel")}>
            <Download className="mr-2 rtl:mr-0 rtl:ml-2 h-4 w-4" />
            Excel
          </Button>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              إجمالي المبيعات
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {salesSummary.totalSales.toFixed(2)} د.ع
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              عدد الطلبات
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{salesSummary.orderCount}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              متوسط قيمة الطلب
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {salesSummary.averageOrderValue.toFixed(2)} د.ع
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <div className="flex flex-wrap gap-4 mb-6">
        <div className="flex items-center gap-2">
          <span className="text-sm font-medium">Date Range:</span>
          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                className="w-[240px] justify-start text-left"
              >
                <CalendarIcon className="mr-2 h-4 w-4" />
                {dateRange.from ? (
                  dateRange.to ? (
                    <>
                      {format(dateRange.from, "LLL dd, y")} -{" "}
                      {format(dateRange.to, "LLL dd, y")}
                    </>
                  ) : (
                    format(dateRange.from, "LLL dd, y")
                  )
                ) : (
                  <span>Pick a date</span>
                )}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0" align="start">
              <Calendar
                mode="range"
                selected={dateRange}
                onSelect={setDateRange}
                initialFocus
              />
            </PopoverContent>
          </Popover>
        </div>

        <div className="flex items-center gap-2">
          <span className="text-sm font-medium">Order Type:</span>
          <Select value={orderTypeFilter} onValueChange={setOrderTypeFilter}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="All Order Types" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Order Types</SelectItem>
              <SelectItem value="dine-in">Dine-in</SelectItem>
              <SelectItem value="delivery">Delivery</SelectItem>
              <SelectItem value="takeaway">Takeaway</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="flex items-center gap-2">
          <span className="text-sm font-medium">Payment Method:</span>
          <Select
            value={paymentMethodFilter}
            onValueChange={setPaymentMethodFilter}
          >
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="All Payment Methods" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Payment Methods</SelectItem>
              <SelectItem value="cash">Cash</SelectItem>
              <SelectItem value="card">Card</SelectItem>
              <SelectItem value="online">Online</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Charts */}
      <Tabs defaultValue="daily" className="mb-6">
        <TabsList>
          <TabsTrigger value="daily">Daily</TabsTrigger>
          <TabsTrigger value="weekly">Weekly</TabsTrigger>
          <TabsTrigger value="monthly">Monthly</TabsTrigger>
        </TabsList>
        <TabsContent value="daily" className="pt-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <LineChart className="mr-2 h-5 w-5" />
                  Daily Sales Trend
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-[300px] flex items-center justify-center bg-muted/20 rounded-md">
                  <p className="text-muted-foreground">
                    Line chart visualization would appear here
                  </p>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <PieChart className="mr-2 h-5 w-5" />
                  Sales by Order Type
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-[300px] flex items-center justify-center bg-muted/20 rounded-md">
                  <p className="text-muted-foreground">
                    Pie chart visualization would appear here
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
        <TabsContent value="weekly" className="pt-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <BarChart className="mr-2 h-5 w-5" />
                  Weekly Sales Comparison
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-[300px] flex items-center justify-center bg-muted/20 rounded-md">
                  <p className="text-muted-foreground">
                    Bar chart visualization would appear here
                  </p>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <PieChart className="mr-2 h-5 w-5" />
                  Weekly Payment Methods
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-[300px] flex items-center justify-center bg-muted/20 rounded-md">
                  <p className="text-muted-foreground">
                    Pie chart visualization would appear here
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
        <TabsContent value="monthly" className="pt-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <LineChart className="mr-2 h-5 w-5" />
                  Monthly Revenue Trend
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-[300px] flex items-center justify-center bg-muted/20 rounded-md">
                  <p className="text-muted-foreground">
                    Line chart visualization would appear here
                  </p>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <BarChart className="mr-2 h-5 w-5" />
                  Top Selling Items
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-[300px] flex items-center justify-center bg-muted/20 rounded-md">
                  <p className="text-muted-foreground">
                    Bar chart visualization would appear here
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>

      {/* Sales Table */}
      <Card>
        <CardHeader>
          <CardTitle>Detailed Sales Records</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Order ID</TableHead>
                <TableHead>Date</TableHead>
                <TableHead>Order Type</TableHead>
                <TableHead>Payment Method</TableHead>
                <TableHead>Items</TableHead>
                <TableHead className="text-right">Amount</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {salesRecords.map((record) => (
                <TableRow key={record.id}>
                  <TableCell>{record.id}</TableCell>
                  <TableCell>{format(record.date, "MMM dd, yyyy")}</TableCell>
                  <TableCell className="capitalize">
                    {record.orderType}
                  </TableCell>
                  <TableCell className="capitalize">
                    {record.paymentMethod}
                  </TableCell>
                  <TableCell>{record.items}</TableCell>
                  <TableCell className="text-right">
                    {record.amount.toFixed(2)} د.ع
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
};

export default ReportsDashboard;
