import React, { useState, useEffect } from "react";
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { format } from "date-fns";
import {
  CalendarIcon,
  Download,
  <PERSON><PERSON>hart,
  <PERSON><PERSON>hart,
  <PERSON><PERSON><PERSON>,
  Printer,
  RefreshCw,
} from "lucide-react";
// import { db, Order } from "@/lib/database";

interface SalesSummary {
  totalSales: number;
  orderCount: number;
  averageOrderValue: number;
}

// Remove the old interface since we're using Order from database

interface ReportsDashboardProps {
  language?: "english" | "arabic";
}

const ReportsDashboard = ({ language = "arabic" }: ReportsDashboardProps) => {
  const [loading, setLoading] = useState(false);

  // Sample data for demonstration
  const sampleOrders = [
    {
      id: "1",
      orderNumber: "ORD001",
      customerName: "أحمد محمد",
      orderType: "dine-in",
      paymentMethod: "cash",
      total: 125.50,
      createdAt: new Date(),
      items: []
    },
    {
      id: "2",
      orderNumber: "ORD002",
      customerName: "فاطمة علي",
      orderType: "delivery",
      paymentMethod: "card",
      total: 89.75,
      createdAt: new Date(),
      items: []
    }
  ];

  const salesSummary = {
    totalSales: 1250.75,
    orderCount: 15,
    averageOrderValue: 83.38
  };

  const loadOrders = () => {
    alert("تم تحديث البيانات");
  };

  const handleExport = (format: "csv" | "pdf" | "excel") => {
    alert(`سيتم تصدير البيانات بصيغة ${format} قريباً`);
  };

  const handlePrintReport = () => {
    alert("سيتم إضافة نظام طباعة التقارير قريباً");
  };

  return (
    <div className="bg-background p-6 w-full h-full">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">تقارير المبيعات</h1>
        <div className="flex gap-2">
          <Button
            onClick={loadOrders}
            variant="outline"
            disabled={loading}
          >
            <RefreshCw className={`mr-2 rtl:mr-0 rtl:ml-2 h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
            تحديث
          </Button>
          <Button
            onClick={handlePrintReport}
            className="bg-primary hover:bg-primary/90"
          >
            <Printer className="mr-2 rtl:mr-0 rtl:ml-2 h-4 w-4" />
            طباعة التقرير
          </Button>
          <Button variant="outline" onClick={() => handleExport("csv")}>
            <Download className="mr-2 rtl:mr-0 rtl:ml-2 h-4 w-4" />
            CSV
          </Button>
          <Button variant="outline" onClick={() => handleExport("pdf")}>
            <Download className="mr-2 rtl:mr-0 rtl:ml-2 h-4 w-4" />
            PDF
          </Button>
          <Button variant="outline" onClick={() => handleExport("excel")}>
            <Download className="mr-2 rtl:mr-0 rtl:ml-2 h-4 w-4" />
            Excel
          </Button>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              إجمالي المبيعات
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {salesSummary.totalSales.toFixed(2)} ر.س
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              عدد الطلبات
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{salesSummary.orderCount}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              متوسط قيمة الطلب
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {salesSummary.averageOrderValue.toFixed(2)} ر.س
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Sample Orders Table */}
      <Card>
        <CardHeader>
          <CardTitle>طلبات العينة</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>رقم الطلب</TableHead>
                <TableHead>العميل</TableHead>
                <TableHead>نوع الطلب</TableHead>
                <TableHead>طريقة الدفع</TableHead>
                <TableHead>المبلغ</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {sampleOrders.map((order) => (
                <TableRow key={order.id}>
                  <TableCell>{order.orderNumber}</TableCell>
                  <TableCell>{order.customerName}</TableCell>
                  <TableCell>
                    {order.orderType === "dine-in" ? "تناول في المطعم" :
                     order.orderType === "delivery" ? "توصيل" : "سفري"}
                  </TableCell>
                  <TableCell>
                    {order.paymentMethod === "cash" ? "نقدي" :
                     order.paymentMethod === "card" ? "بطاقة" : "أونلاين"}
                  </TableCell>
                  <TableCell>{order.total.toFixed(2)} ر.س</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
};

export default ReportsDashboard;
