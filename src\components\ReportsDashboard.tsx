import React, { useState, useEffect } from "react";
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { format } from "date-fns";
import {
  CalendarIcon,
  Download,
  <PERSON><PERSON>hart,
  <PERSON><PERSON>hart,
  <PERSON><PERSON><PERSON>,
  Printer,
  RefreshCw,
} from "lucide-react";
import { db, Order } from "@/lib/database";

interface SalesSummary {
  totalSales: number;
  orderCount: number;
  averageOrderValue: number;
}

// Remove the old interface since we're using Order from database

interface ReportsDashboardProps {
  language?: "english" | "arabic";
}

const ReportsDashboard = ({ language = "arabic" }: ReportsDashboardProps) => {
  const [dateRange, setDateRange] = useState<{
    from: Date;
    to?: Date;
  }>({ from: new Date(new Date().setDate(new Date().getDate() - 7)) });

  const [orderTypeFilter, setOrderTypeFilter] = useState<string>("all");
  const [paymentMethodFilter, setPaymentMethodFilter] = useState<string>("all");
  const [timeFilter, setTimeFilter] = useState<string>("all");
  const [quickFilter, setQuickFilter] = useState<string>("week");
  const [searchTerm, setSearchTerm] = useState<string>("");
  const [minAmount, setMinAmount] = useState<string>("");
  const [maxAmount, setMaxAmount] = useState<string>("");
  const [orders, setOrders] = useState<Order[]>([]);
  const [loading, setLoading] = useState(true);

  // Load orders from database
  useEffect(() => {
    loadOrders();
  }, []);

  const loadOrders = () => {
    setLoading(true);
    try {
      const allOrders = db.getOrders();
      setOrders(allOrders);
    } catch (error) {
      console.error("Error loading orders:", error);
    } finally {
      setLoading(false);
    }
  };

  // Quick filter functions
  const applyQuickFilter = (filter: string) => {
    const now = new Date();
    let from: Date;
    let to: Date = new Date(now);

    switch (filter) {
      case "today":
        from = new Date(now);
        from.setHours(0, 0, 0, 0);
        to.setHours(23, 59, 59, 999);
        break;
      case "yesterday":
        from = new Date(now);
        from.setDate(now.getDate() - 1);
        from.setHours(0, 0, 0, 0);
        to = new Date(from);
        to.setHours(23, 59, 59, 999);
        break;
      case "week":
        from = new Date(now);
        from.setDate(now.getDate() - 7);
        break;
      case "month":
        from = new Date(now);
        from.setMonth(now.getMonth() - 1);
        break;
      case "year":
        from = new Date(now);
        from.setFullYear(now.getFullYear() - 1);
        break;
      default:
        return;
    }

    setDateRange({ from, to });
    setQuickFilter(filter);
  };

  // Advanced filtering function
  const getFilteredRecords = () => {
    return orders.filter(order => {
      // Date range filter
      if (dateRange.from && order.createdAt < dateRange.from) return false;
      if (dateRange.to && order.createdAt > dateRange.to) return false;

      // Order type filter
      if (orderTypeFilter !== "all" && order.orderType !== orderTypeFilter) return false;

      // Payment method filter
      if (paymentMethodFilter !== "all" && order.paymentMethod !== paymentMethodFilter) return false;

      // Time filter (hour of day)
      if (timeFilter !== "all") {
        const hour = order.createdAt.getHours();
        switch (timeFilter) {
          case "morning":
            if (hour < 6 || hour >= 12) return false;
            break;
          case "afternoon":
            if (hour < 12 || hour >= 18) return false;
            break;
          case "evening":
            if (hour < 18 || hour >= 24) return false;
            break;
          case "night":
            if (hour >= 6) return false;
            break;
        }
      }

      // Search term filter
      if (searchTerm &&
          !order.orderNumber.toLowerCase().includes(searchTerm.toLowerCase()) &&
          !order.customerName.toLowerCase().includes(searchTerm.toLowerCase())) {
        return false;
      }

      // Amount range filter
      if (minAmount && order.total < parseFloat(minAmount)) return false;
      if (maxAmount && order.total > parseFloat(maxAmount)) return false;

      return true;
    });
  };

  const filteredRecords = getFilteredRecords();

  // Calculate filtered summary
  const salesSummary: SalesSummary = {
    totalSales: filteredRecords.reduce((sum, order) => sum + order.total, 0),
    orderCount: filteredRecords.length,
    averageOrderValue: filteredRecords.length > 0
      ? filteredRecords.reduce((sum, order) => sum + order.total, 0) / filteredRecords.length
      : 0,
  };

  const handleExport = (format: "csv" | "pdf" | "excel") => {
    // Placeholder for export functionality
    console.log(`Exporting data in ${format} format`);
  };

  const handlePrintReport = () => {
    try {
      const currentDate = new Date().toLocaleDateString("ar-SA");
      const currentTime = new Date().toLocaleTimeString("ar-SA");

      // Create print content
      const printContent = `
        <html dir="rtl">
          <head>
            <title>تقرير المبيعات</title>
            <meta charset="UTF-8">
            <style>
              @media print {
                body { margin: 0; }
                .no-print { display: none !important; }
              }
              body {
                font-family: 'Arial', sans-serif;
                margin: 20px;
                direction: rtl;
                color: #333;
                line-height: 1.6;
              }
              .header {
                text-align: center;
                border-bottom: 2px solid #333;
                padding-bottom: 15px;
                margin-bottom: 25px;
              }
              .header h1 { margin: 0; color: #2c3e50; }
              .header h2 { margin: 10px 0; color: #34495e; }
              .summary { margin: 25px 0; }
              .summary-grid {
                display: grid;
                grid-template-columns: repeat(3, 1fr);
                gap: 15px;
                margin: 20px 0;
              }
              .summary-card {
                border: 1px solid #ddd;
                padding: 15px;
                border-radius: 8px;
                background: #f8f9fa;
                text-align: center;
              }
              .summary-card strong {
                display: block;
                font-size: 14px;
                color: #666;
                margin-bottom: 8px;
              }
              .summary-card .value {
                font-size: 20px;
                font-weight: bold;
                color: #2c3e50;
              }
              .records { margin: 25px 0; }
              table {
                width: 100%;
                border-collapse: collapse;
                margin: 20px 0;
                font-size: 12px;
              }
              th, td {
                border: 1px solid #ddd;
                padding: 10px 8px;
                text-align: right;
              }
              th {
                background-color: #34495e;
                color: white;
                font-weight: bold;
              }
              tr:nth-child(even) { background-color: #f8f9fa; }
              .total {
                border-top: 2px solid #333;
                padding-top: 15px;
                font-weight: bold;
                font-size: 18px;
                text-align: center;
                margin-top: 25px;
              }
              .footer {
                text-align: center;
                margin-top: 30px;
                padding-top: 20px;
                border-top: 1px solid #ddd;
                color: #666;
              }
            </style>
          </head>
          <body>
            <div class="header">
              <h1>مطعم الذواقة</h1>
              <h2>تقرير المبيعات</h2>
              <p>التاريخ: ${currentDate} | الوقت: ${currentTime}</p>
            </div>

            <div class="summary">
              <h3>ملخص المبيعات</h3>
              <div class="summary-grid">
                <div class="summary-card">
                  <strong>إجمالي المبيعات</strong>
                  <div class="value">${salesSummary.totalSales.toFixed(2)} ر.س</div>
                </div>
                <div class="summary-card">
                  <strong>عدد الطلبات</strong>
                  <div class="value">${salesSummary.orderCount}</div>
                </div>
                <div class="summary-card">
                  <strong>متوسط قيمة الطلب</strong>
                  <div class="value">${salesSummary.averageOrderValue.toFixed(2)} ر.س</div>
                </div>
              </div>
            </div>

            <div class="records">
              <h3>سجلات المبيعات التفصيلية (أول 50 سجل)</h3>
              <table>
                <thead>
                  <tr>
                    <th>رقم الطلب</th>
                    <th>التاريخ والوقت</th>
                    <th>العميل</th>
                    <th>نوع الطلب</th>
                    <th>طريقة الدفع</th>
                    <th>عدد العناصر</th>
                    <th>المبلغ</th>
                  </tr>
                </thead>
                <tbody>
                  ${filteredRecords.slice(0, 50)
                    .map(
                      (order) => `
                    <tr>
                      <td>${order.orderNumber}</td>
                      <td>${format(order.createdAt, "dd/MM/yyyy HH:mm")}</td>
                      <td>${order.customerName}</td>
                      <td>${order.orderType === "dine-in" ? "تناول في المطعم" :
                           order.orderType === "delivery" ? "توصيل" : "سفري"}</td>
                      <td>${order.paymentMethod === "cash" ? "نقدي" :
                           order.paymentMethod === "card" ? "بطاقة" : "أونلاين"}</td>
                      <td>${order.items.length}</td>
                      <td>${order.total.toFixed(2)} ر.س</td>
                    </tr>
                  `,
                    )
                    .join("")}
                </tbody>
              </table>
            </div>

            <div class="total">
              <p>إجمالي المبيعات المعروضة: ${salesSummary.totalSales.toFixed(2)} ر.س</p>
            </div>

            <div class="footer">
              <p>تم إنشاء هذا التقرير بواسطة نظام إدارة المطعم</p>
              <p>طُبع في: ${currentDate} - ${currentTime}</p>
            </div>
          </body>
        </html>
      `;

      // Open print window
      const printWindow = window.open("", "_blank", "width=800,height=600");

      if (printWindow) {
        printWindow.document.write(printContent);
        printWindow.document.close();

        // Wait for content to load then print
        printWindow.onload = () => {
          setTimeout(() => {
            printWindow.print();
            // Close window after printing
            setTimeout(() => {
              printWindow.close();
            }, 1000);
          }, 500);
        };
      } else {
        alert("تعذر فتح نافذة الطباعة. يرجى التأكد من عدم حظر النوافذ المنبثقة.");
      }
    } catch (error) {
      console.error("خطأ في الطباعة:", error);
      alert("حدث خطأ أثناء الطباعة. يرجى المحاولة مرة أخرى.");
    }
  };

  return (
    <div className="bg-background p-6 w-full h-full">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">تقارير المبيعات</h1>
        <div className="flex gap-2">
          <Button
            onClick={loadOrders}
            variant="outline"
            disabled={loading}
          >
            <RefreshCw className={`mr-2 rtl:mr-0 rtl:ml-2 h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
            تحديث
          </Button>
          <Button
            onClick={handlePrintReport}
            className="bg-primary hover:bg-primary/90"
          >
            <Printer className="mr-2 rtl:mr-0 rtl:ml-2 h-4 w-4" />
            طباعة التقرير
          </Button>
          <Button variant="outline" onClick={() => handleExport("csv")}>
            <Download className="mr-2 rtl:mr-0 rtl:ml-2 h-4 w-4" />
            CSV
          </Button>
          <Button variant="outline" onClick={() => handleExport("pdf")}>
            <Download className="mr-2 rtl:mr-0 rtl:ml-2 h-4 w-4" />
            PDF
          </Button>
          <Button variant="outline" onClick={() => handleExport("excel")}>
            <Download className="mr-2 rtl:mr-0 rtl:ml-2 h-4 w-4" />
            Excel
          </Button>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              إجمالي المبيعات
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {salesSummary.totalSales.toFixed(2)} د.ع
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              عدد الطلبات
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{salesSummary.orderCount}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              متوسط قيمة الطلب
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {salesSummary.averageOrderValue.toFixed(2)} د.ع
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Quick Filters */}
      <div className="mb-4">
        <span className="text-sm font-medium mb-2 block">مرشحات سريعة:</span>
        <div className="flex flex-wrap gap-2">
          {[
            { value: "today", label: "اليوم" },
            { value: "yesterday", label: "أمس" },
            { value: "week", label: "آخر أسبوع" },
            { value: "month", label: "آخر شهر" },
            { value: "year", label: "آخر سنة" },
          ].map((filter) => (
            <Button
              key={filter.value}
              variant={quickFilter === filter.value ? "default" : "outline"}
              size="sm"
              onClick={() => applyQuickFilter(filter.value)}
            >
              {filter.label}
            </Button>
          ))}
        </div>
      </div>

      {/* Advanced Filters */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        <div className="flex flex-col gap-2">
          <span className="text-sm font-medium">نطاق التاريخ:</span>
          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                className="w-full justify-start text-left"
              >
                <CalendarIcon className="mr-2 h-4 w-4" />
                {dateRange.from ? (
                  dateRange.to ? (
                    <>
                      {format(dateRange.from, "dd/MM")} -{" "}
                      {format(dateRange.to, "dd/MM")}
                    </>
                  ) : (
                    format(dateRange.from, "dd/MM/yyyy")
                  )
                ) : (
                  <span>اختر تاريخ</span>
                )}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0" align="start">
              <Calendar
                mode="range"
                selected={dateRange}
                onSelect={setDateRange}
                initialFocus
              />
            </PopoverContent>
          </Popover>
        </div>

        <div className="flex flex-col gap-2">
          <span className="text-sm font-medium">فترة اليوم:</span>
          <Select value={timeFilter} onValueChange={setTimeFilter}>
            <SelectTrigger>
              <SelectValue placeholder="جميع الأوقات" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">جميع الأوقات</SelectItem>
              <SelectItem value="morning">الصباح (6ص - 12ظ)</SelectItem>
              <SelectItem value="afternoon">بعد الظهر (12ظ - 6م)</SelectItem>
              <SelectItem value="evening">المساء (6م - 12ص)</SelectItem>
              <SelectItem value="night">الليل (12ص - 6ص)</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="flex flex-col gap-2">
          <span className="text-sm font-medium">نوع الطلب:</span>
          <Select value={orderTypeFilter} onValueChange={setOrderTypeFilter}>
            <SelectTrigger>
              <SelectValue placeholder="جميع أنواع الطلبات" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">جميع أنواع الطلبات</SelectItem>
              <SelectItem value="dine-in">تناول في المطعم</SelectItem>
              <SelectItem value="delivery">توصيل</SelectItem>
              <SelectItem value="takeaway">سفري</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="flex flex-col gap-2">
          <span className="text-sm font-medium">طريقة الدفع:</span>
          <Select
            value={paymentMethodFilter}
            onValueChange={setPaymentMethodFilter}
          >
            <SelectTrigger>
              <SelectValue placeholder="جميع طرق الدفع" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">جميع طرق الدفع</SelectItem>
              <SelectItem value="cash">نقدي</SelectItem>
              <SelectItem value="card">بطاقة</SelectItem>
              <SelectItem value="online">أونلاين</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Search and Amount Filters */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <div className="flex flex-col gap-2">
          <span className="text-sm font-medium">البحث:</span>
          <Input
            placeholder="رقم الطلب أو اسم العميل..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>

        <div className="flex flex-col gap-2">
          <span className="text-sm font-medium">الحد الأدنى للمبلغ:</span>
          <Input
            type="number"
            placeholder="0.00"
            value={minAmount}
            onChange={(e) => setMinAmount(e.target.value)}
          />
        </div>

        <div className="flex flex-col gap-2">
          <span className="text-sm font-medium">الحد الأقصى للمبلغ:</span>
          <Input
            type="number"
            placeholder="1000.00"
            value={maxAmount}
            onChange={(e) => setMaxAmount(e.target.value)}
          />
        </div>
      </div>

      {/* Filter Results Summary */}
      <div className="mb-6 p-4 bg-muted/20 rounded-lg">
        <div className="flex justify-between items-center">
          <span className="text-sm text-muted-foreground">
            عرض {filteredRecords.length} من أصل {orders.length} سجل
          </span>
          <Button
            variant="outline"
            size="sm"
            onClick={() => {
              setDateRange({ from: new Date(new Date().setDate(new Date().getDate() - 7)) });
              setOrderTypeFilter("all");
              setPaymentMethodFilter("all");
              setTimeFilter("all");
              setQuickFilter("week");
              setSearchTerm("");
              setMinAmount("");
              setMaxAmount("");
            }}
          >
            إعادة تعيين المرشحات
          </Button>
        </div>
      </div>

      {/* Charts */}
      <Tabs defaultValue="daily" className="mb-6">
        <TabsList>
          <TabsTrigger value="daily">يومي</TabsTrigger>
          <TabsTrigger value="weekly">أسبوعي</TabsTrigger>
          <TabsTrigger value="monthly">شهري</TabsTrigger>
        </TabsList>
        <TabsContent value="daily" className="pt-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <LineChart className="mr-2 h-5 w-5" />
                  اتجاه المبيعات اليومية
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-[300px] flex items-center justify-center bg-muted/20 rounded-md">
                  <p className="text-muted-foreground">
                    سيظهر هنا الرسم البياني الخطي
                  </p>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <PieChart className="mr-2 h-5 w-5" />
                  المبيعات حسب نوع الطلب
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-[300px] flex items-center justify-center bg-muted/20 rounded-md">
                  <p className="text-muted-foreground">
                    سيظهر هنا الرسم البياني الدائري
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
        <TabsContent value="weekly" className="pt-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <BarChart className="mr-2 h-5 w-5" />
                  مقارنة المبيعات الأسبوعية
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-[300px] flex items-center justify-center bg-muted/20 rounded-md">
                  <p className="text-muted-foreground">
                    سيظهر هنا الرسم البياني العمودي
                  </p>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <PieChart className="mr-2 h-5 w-5" />
                  طرق الدفع الأسبوعية
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-[300px] flex items-center justify-center bg-muted/20 rounded-md">
                  <p className="text-muted-foreground">
                    سيظهر هنا الرسم البياني الدائري
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
        <TabsContent value="monthly" className="pt-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <LineChart className="mr-2 h-5 w-5" />
                  اتجاه الإيرادات الشهرية
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-[300px] flex items-center justify-center bg-muted/20 rounded-md">
                  <p className="text-muted-foreground">
                    سيظهر هنا الرسم البياني الخطي
                  </p>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <BarChart className="mr-2 h-5 w-5" />
                  أكثر العناصر مبيعاً
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-[300px] flex items-center justify-center bg-muted/20 rounded-md">
                  <p className="text-muted-foreground">
                    سيظهر هنا الرسم البياني العمودي
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>

      {/* Sales Table */}
      <Card>
        <CardHeader>
          <CardTitle>سجلات المبيعات التفصيلية</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>رقم الطلب</TableHead>
                <TableHead>التاريخ والوقت</TableHead>
                <TableHead>العميل</TableHead>
                <TableHead>نوع الطلب</TableHead>
                <TableHead>طريقة الدفع</TableHead>
                <TableHead>عدد العناصر</TableHead>
                <TableHead className="text-right">المبلغ</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell colSpan={7} className="text-center py-8">
                    <RefreshCw className="h-6 w-6 animate-spin mx-auto mb-2" />
                    جاري تحميل البيانات...
                  </TableCell>
                </TableRow>
              ) : filteredRecords.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={7} className="text-center py-8 text-muted-foreground">
                    لا توجد طلبات تطابق المرشحات المحددة
                  </TableCell>
                </TableRow>
              ) : (
                filteredRecords.slice(0, 50).map((order) => (
                  <TableRow key={order.id}>
                    <TableCell className="font-medium">{order.orderNumber}</TableCell>
                    <TableCell>
                      <div className="flex flex-col">
                        <span>{format(order.createdAt, "dd/MM/yyyy")}</span>
                        <span className="text-xs text-muted-foreground">
                          {format(order.createdAt, "HH:mm:ss")}
                        </span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex flex-col">
                        <span>{order.customerName}</span>
                        {order.orderType === "dine-in" && order.tableNumber && (
                          <span className="text-xs text-muted-foreground">
                            طاولة {order.tableNumber}
                          </span>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <span className={`px-2 py-1 rounded-full text-xs order-${order.orderType}`}>
                        {order.orderType === "dine-in" ? "تناول في المطعم" :
                         order.orderType === "delivery" ? "توصيل" : "سفري"}
                      </span>
                    </TableCell>
                    <TableCell>
                      <span className={`px-2 py-1 rounded-full text-xs payment-${order.paymentMethod}`}>
                        {order.paymentMethod === "cash" ? "نقدي" :
                         order.paymentMethod === "card" ? "بطاقة" : "أونلاين"}
                      </span>
                    </TableCell>
                    <TableCell>{order.items.length}</TableCell>
                    <TableCell className="text-right font-medium">
                      {order.total.toFixed(2)} ر.س
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
          {filteredRecords.length > 50 && (
            <div className="mt-4 text-center text-sm text-muted-foreground">
              عرض أول 50 سجل من أصل {filteredRecords.length} سجل
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default ReportsDashboard;
