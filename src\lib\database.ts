// Restaurant Database Management System
// Using localStorage for data persistence

export interface MenuItem {
  id: string;
  name: string;
  nameEn: string;
  category: string;
  price: number;
  description?: string;
  image?: string;
  available: boolean;
  preparationTime: number; // in minutes
  ingredients: string[];
  allergens: string[];
  createdAt: Date;
  updatedAt: Date;
}

export interface Category {
  id: string;
  name: string;
  nameEn: string;
  description?: string;
  displayOrder: number;
  active: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface Customer {
  id: string;
  name: string;
  phone?: string;
  email?: string;
  address?: string;
  totalOrders: number;
  totalSpent: number;
  lastOrderDate?: Date;
  createdAt: Date;
  updatedAt: Date;
}

export interface OrderItem {
  id: string;
  menuItemId: string;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
  notes?: string;
  modifications?: string[];
}

export interface Order {
  id: string;
  orderNumber: string;
  customerId?: string;
  customerName: string;
  customerPhone?: string;
  items: OrderItem[];
  subtotal: number;
  tax: number;
  discount: number;
  total: number;
  orderType: "dine-in" | "delivery" | "takeaway";
  paymentMethod: "cash" | "card" | "online";
  status: "pending" | "preparing" | "ready" | "delivered" | "cancelled";
  tableNumber?: number;
  deliveryAddress?: string;
  notes?: string;
  createdAt: Date;
  updatedAt: Date;
  completedAt?: Date;
  createdBy: string; // user ID
}

export interface User {
  id: string;
  username: string;
  password: string; // In real app, this should be hashed
  name: string;
  role: "admin" | "cashier" | "kitchen" | "waiter";
  permissions: string[];
  active: boolean;
  lastLogin?: Date;
  createdAt: Date;
  updatedAt: Date;
}

export interface RestaurantSettings {
  id: string;
  name: string;
  nameEn: string;
  address: string;
  phone: string;
  email: string;
  taxRate: number;
  currency: string;
  currencySymbol: string;
  timezone: string;
  workingHours: {
    [key: string]: { open: string; close: string; closed: boolean };
  };
  deliveryFee: number;
  minimumDeliveryOrder: number;
  updatedAt: Date;
}

// Database class for managing all data operations
export class RestaurantDatabase {
  private static instance: RestaurantDatabase;
  
  private constructor() {
    this.initializeDatabase();
  }

  public static getInstance(): RestaurantDatabase {
    if (!RestaurantDatabase.instance) {
      RestaurantDatabase.instance = new RestaurantDatabase();
    }
    return RestaurantDatabase.instance;
  }

  private initializeDatabase(): void {
    // Initialize with default data if not exists
    if (!this.getSettings()) {
      this.initializeDefaultData();
    }
  }

  // Generic CRUD operations
  private getFromStorage<T>(key: string): T[] {
    const data = localStorage.getItem(key);
    return data ? JSON.parse(data) : [];
  }

  private saveToStorage<T>(key: string, data: T[]): void {
    localStorage.setItem(key, JSON.stringify(data));
  }

  private generateId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }

  // Settings operations
  getSettings(): RestaurantSettings | null {
    const settings = localStorage.getItem('restaurant_settings');
    return settings ? JSON.parse(settings) : null;
  }

  saveSettings(settings: RestaurantSettings): void {
    settings.updatedAt = new Date();
    localStorage.setItem('restaurant_settings', JSON.stringify(settings));
  }

  // Categories operations
  getCategories(): Category[] {
    return this.getFromStorage<Category>('restaurant_categories')
      .map(cat => ({
        ...cat,
        createdAt: new Date(cat.createdAt),
        updatedAt: new Date(cat.updatedAt)
      }))
      .sort((a, b) => a.displayOrder - b.displayOrder);
  }

  saveCategory(category: Omit<Category, 'id' | 'createdAt' | 'updatedAt'>): Category {
    const categories = this.getCategories();
    const newCategory: Category = {
      ...category,
      id: this.generateId(),
      createdAt: new Date(),
      updatedAt: new Date()
    };
    categories.push(newCategory);
    this.saveToStorage('restaurant_categories', categories);
    return newCategory;
  }

  updateCategory(id: string, updates: Partial<Category>): Category | null {
    const categories = this.getCategories();
    const index = categories.findIndex(cat => cat.id === id);
    if (index === -1) return null;
    
    categories[index] = {
      ...categories[index],
      ...updates,
      updatedAt: new Date()
    };
    this.saveToStorage('restaurant_categories', categories);
    return categories[index];
  }

  deleteCategory(id: string): boolean {
    const categories = this.getCategories();
    const filteredCategories = categories.filter(cat => cat.id !== id);
    if (filteredCategories.length === categories.length) return false;
    
    this.saveToStorage('restaurant_categories', filteredCategories);
    return true;
  }

  // Menu items operations
  getMenuItems(): MenuItem[] {
    return this.getFromStorage<MenuItem>('restaurant_menu_items')
      .map(item => ({
        ...item,
        createdAt: new Date(item.createdAt),
        updatedAt: new Date(item.updatedAt)
      }));
  }

  getMenuItemsByCategory(categoryId: string): MenuItem[] {
    return this.getMenuItems().filter(item => item.category === categoryId);
  }

  saveMenuItem(item: Omit<MenuItem, 'id' | 'createdAt' | 'updatedAt'>): MenuItem {
    const items = this.getMenuItems();
    const newItem: MenuItem = {
      ...item,
      id: this.generateId(),
      createdAt: new Date(),
      updatedAt: new Date()
    };
    items.push(newItem);
    this.saveToStorage('restaurant_menu_items', items);
    return newItem;
  }

  updateMenuItem(id: string, updates: Partial<MenuItem>): MenuItem | null {
    const items = this.getMenuItems();
    const index = items.findIndex(item => item.id === id);
    if (index === -1) return null;
    
    items[index] = {
      ...items[index],
      ...updates,
      updatedAt: new Date()
    };
    this.saveToStorage('restaurant_menu_items', items);
    return items[index];
  }

  deleteMenuItem(id: string): boolean {
    const items = this.getMenuItems();
    const filteredItems = items.filter(item => item.id !== id);
    if (filteredItems.length === items.length) return false;
    
    this.saveToStorage('restaurant_menu_items', filteredItems);
    return true;
  }

  // Orders operations
  getOrders(): Order[] {
    return this.getFromStorage<Order>('restaurant_orders')
      .map(order => ({
        ...order,
        createdAt: new Date(order.createdAt),
        updatedAt: new Date(order.updatedAt),
        completedAt: order.completedAt ? new Date(order.completedAt) : undefined
      }))
      .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());
  }

  saveOrder(order: Omit<Order, 'id' | 'orderNumber' | 'createdAt' | 'updatedAt'>): Order {
    const orders = this.getOrders();
    const orderNumber = `ORD${Date.now()}`;
    const newOrder: Order = {
      ...order,
      id: this.generateId(),
      orderNumber,
      createdAt: new Date(),
      updatedAt: new Date()
    };
    orders.push(newOrder);
    this.saveToStorage('restaurant_orders', orders);
    return newOrder;
  }

  updateOrder(id: string, updates: Partial<Order>): Order | null {
    const orders = this.getOrders();
    const index = orders.findIndex(order => order.id === id);
    if (index === -1) return null;
    
    orders[index] = {
      ...orders[index],
      ...updates,
      updatedAt: new Date()
    };
    this.saveToStorage('restaurant_orders', orders);
    return orders[index];
  }

  // Customers operations
  getCustomers(): Customer[] {
    return this.getFromStorage<Customer>('restaurant_customers')
      .map(customer => ({
        ...customer,
        createdAt: new Date(customer.createdAt),
        updatedAt: new Date(customer.updatedAt),
        lastOrderDate: customer.lastOrderDate ? new Date(customer.lastOrderDate) : undefined
      }));
  }

  saveCustomer(customer: Omit<Customer, 'id' | 'createdAt' | 'updatedAt'>): Customer {
    const customers = this.getCustomers();
    const newCustomer: Customer = {
      ...customer,
      id: this.generateId(),
      createdAt: new Date(),
      updatedAt: new Date()
    };
    customers.push(newCustomer);
    this.saveToStorage('restaurant_customers', customers);
    return newCustomer;
  }

  findCustomerByPhone(phone: string): Customer | null {
    return this.getCustomers().find(customer => customer.phone === phone) || null;
  }

  // Users operations
  getUsers(): User[] {
    return this.getFromStorage<User>('restaurant_users')
      .map(user => ({
        ...user,
        createdAt: new Date(user.createdAt),
        updatedAt: new Date(user.updatedAt),
        lastLogin: user.lastLogin ? new Date(user.lastLogin) : undefined
      }));
  }

  findUserByUsername(username: string): User | null {
    return this.getUsers().find(user => user.username === username) || null;
  }

  updateUserLastLogin(id: string): void {
    const users = this.getUsers();
    const index = users.findIndex(user => user.id === id);
    if (index !== -1) {
      users[index].lastLogin = new Date();
      users[index].updatedAt = new Date();
      this.saveToStorage('restaurant_users', users);
    }
  }

  // Initialize default data
  private initializeDefaultData(): void {
    // Default settings
    const defaultSettings: RestaurantSettings = {
      id: this.generateId(),
      name: "مطعم الذواقة",
      nameEn: "Gourmet Restaurant",
      address: "شارع الملك فهد، الرياض، المملكة العربية السعودية",
      phone: "+966 11 123 4567",
      email: "<EMAIL>",
      taxRate: 0.15,
      currency: "SAR",
      currencySymbol: "ر.س",
      timezone: "Asia/Riyadh",
      workingHours: {
        sunday: { open: "11:00", close: "23:00", closed: false },
        monday: { open: "11:00", close: "23:00", closed: false },
        tuesday: { open: "11:00", close: "23:00", closed: false },
        wednesday: { open: "11:00", close: "23:00", closed: false },
        thursday: { open: "11:00", close: "23:00", closed: false },
        friday: { open: "14:00", close: "23:00", closed: false },
        saturday: { open: "11:00", close: "23:00", closed: false }
      },
      deliveryFee: 15,
      minimumDeliveryOrder: 50,
      updatedAt: new Date()
    };
    this.saveSettings(defaultSettings);

    // Default users
    const defaultUsers: User[] = [
      {
        id: this.generateId(),
        username: "admin",
        password: "admin123", // In production, this should be hashed
        name: "مدير النظام",
        role: "admin",
        permissions: ["all"],
        active: true,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        id: this.generateId(),
        username: "cashier",
        password: "cashier123",
        name: "الكاشير",
        role: "cashier",
        permissions: ["orders", "customers"],
        active: true,
        createdAt: new Date(),
        updatedAt: new Date()
      }
    ];
    this.saveToStorage('restaurant_users', defaultUsers);

    // Default categories
    const defaultCategories: Category[] = [
      {
        id: this.generateId(),
        name: "المقبلات",
        nameEn: "Appetizers",
        description: "مقبلات شهية لبداية مثالية",
        displayOrder: 1,
        active: true,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        id: this.generateId(),
        name: "الأطباق الرئيسية",
        nameEn: "Main Courses",
        description: "أطباق رئيسية متنوعة ولذيذة",
        displayOrder: 2,
        active: true,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        id: this.generateId(),
        name: "المشروبات",
        nameEn: "Beverages",
        description: "مشروبات منعشة ومتنوعة",
        displayOrder: 3,
        active: true,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        id: this.generateId(),
        name: "الحلويات",
        nameEn: "Desserts",
        description: "حلويات شهية لختام مثالي",
        displayOrder: 4,
        active: true,
        createdAt: new Date(),
        updatedAt: new Date()
      }
    ];
    this.saveToStorage('restaurant_categories', defaultCategories);

    // Default menu items will be added in the next step
    this.initializeDefaultMenuItems(defaultCategories);
  }

  private initializeDefaultMenuItems(categories: Category[]): void {
    const appetizers = categories.find(cat => cat.nameEn === "Appetizers")!;
    const mainCourses = categories.find(cat => cat.nameEn === "Main Courses")!;
    const beverages = categories.find(cat => cat.nameEn === "Beverages")!;
    const desserts = categories.find(cat => cat.nameEn === "Desserts")!;

    const defaultMenuItems: MenuItem[] = [
      // Appetizers
      {
        id: this.generateId(),
        name: "حمص بالطحينة",
        nameEn: "Hummus with Tahini",
        category: appetizers.id,
        price: 18,
        description: "حمص كريمي مع الطحينة وزيت الزيتون",
        available: true,
        preparationTime: 5,
        ingredients: ["حمص", "طحينة", "زيت زيتون", "ليمون", "ثوم"],
        allergens: ["سمسم"],
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        id: this.generateId(),
        name: "فتوش",
        nameEn: "Fattoush",
        category: appetizers.id,
        price: 22,
        description: "سلطة خضار مشكلة مع الخبز المحمص",
        available: true,
        preparationTime: 8,
        ingredients: ["خس", "طماطم", "خيار", "فجل", "خبز محمص", "سماق"],
        allergens: ["جلوتين"],
        createdAt: new Date(),
        updatedAt: new Date()
      },
      // Main Courses
      {
        id: this.generateId(),
        name: "كبسة دجاج",
        nameEn: "Chicken Kabsa",
        category: mainCourses.id,
        price: 45,
        description: "أرز بسمتي مع دجاج مشوي وخلطة البهارات الخاصة",
        available: true,
        preparationTime: 25,
        ingredients: ["دجاج", "أرز بسمتي", "بصل", "طماطم", "بهارات كبسة"],
        allergens: [],
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        id: this.generateId(),
        name: "مندي لحم",
        nameEn: "Lamb Mandi",
        category: mainCourses.id,
        price: 65,
        description: "لحم خروف طري مع أرز مندي مدخن",
        available: true,
        preparationTime: 35,
        ingredients: ["لحم خروف", "أرز بسمتي", "بهارات مندي", "زبيب", "لوز"],
        allergens: ["مكسرات"],
        createdAt: new Date(),
        updatedAt: new Date()
      },
      // Beverages
      {
        id: this.generateId(),
        name: "عصير برتقال طازج",
        nameEn: "Fresh Orange Juice",
        category: beverages.id,
        price: 12,
        description: "عصير برتقال طبيعي 100%",
        available: true,
        preparationTime: 3,
        ingredients: ["برتقال طازج"],
        allergens: [],
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        id: this.generateId(),
        name: "شاي أحمر",
        nameEn: "Black Tea",
        category: beverages.id,
        price: 8,
        description: "شاي أحمر تقليدي مع السكر",
        available: true,
        preparationTime: 5,
        ingredients: ["شاي أحمر", "سكر", "ماء"],
        allergens: [],
        createdAt: new Date(),
        updatedAt: new Date()
      },
      // Desserts
      {
        id: this.generateId(),
        name: "كنافة نابلسية",
        nameEn: "Nabulsi Kunafa",
        category: desserts.id,
        price: 25,
        description: "كنافة محشوة بالجبن مع القطر",
        available: true,
        preparationTime: 15,
        ingredients: ["عجينة كنافة", "جبن", "قطر", "فستق"],
        allergens: ["جلوتين", "ألبان", "مكسرات"],
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        id: this.generateId(),
        name: "مهلبية",
        nameEn: "Muhallabia",
        category: desserts.id,
        price: 15,
        description: "حلى كريمي بالحليب مع ماء الورد",
        available: true,
        preparationTime: 10,
        ingredients: ["حليب", "سكر", "نشا", "ماء ورد", "فستق"],
        allergens: ["ألبان", "مكسرات"],
        createdAt: new Date(),
        updatedAt: new Date()
      }
    ];

    this.saveToStorage('restaurant_menu_items', defaultMenuItems);

    // Add some sample orders for testing
    this.initializeSampleOrders();
  }

  private initializeSampleOrders(): void {
    const sampleOrders: Omit<Order, 'id' | 'orderNumber' | 'createdAt' | 'updatedAt'>[] = [];
    const now = new Date();

    // Generate sample orders for the last 30 days
    for (let i = 0; i < 50; i++) {
      const randomDaysAgo = Math.floor(Math.random() * 30);
      const randomHour = Math.floor(Math.random() * 14) + 8; // 8 AM to 10 PM
      const randomMinute = Math.floor(Math.random() * 60);

      const orderDate = new Date(now);
      orderDate.setDate(now.getDate() - randomDaysAgo);
      orderDate.setHours(randomHour, randomMinute, 0);

      const orderTypes: ("dine-in" | "delivery" | "takeaway")[] = ["dine-in", "delivery", "takeaway"];
      const paymentMethods: ("cash" | "card" | "online")[] = ["cash", "card", "online"];
      const statuses: ("pending" | "preparing" | "ready" | "delivered" | "cancelled")[] =
        ["delivered", "delivered", "delivered", "delivered", "pending", "preparing", "ready"];

      const orderType = orderTypes[Math.floor(Math.random() * orderTypes.length)];
      const subtotal = Math.round((Math.random() * 150 + 30) * 100) / 100;
      const tax = subtotal * 0.15;
      const total = subtotal + tax;

      const sampleOrder = {
        customerId: undefined,
        customerName: `عميل ${i + 1}`,
        customerPhone: `05${Math.floor(Math.random() * 90000000) + 10000000}`,
        items: [
          {
            id: `item_${i}_1`,
            menuItemId: "sample_item_1",
            quantity: Math.floor(Math.random() * 3) + 1,
            unitPrice: 25,
            totalPrice: 25 * (Math.floor(Math.random() * 3) + 1),
            notes: "",
          }
        ],
        subtotal,
        tax,
        discount: 0,
        total,
        orderType,
        paymentMethod: paymentMethods[Math.floor(Math.random() * paymentMethods.length)],
        status: statuses[Math.floor(Math.random() * statuses.length)] as any,
        tableNumber: orderType === "dine-in" ? Math.floor(Math.random() * 20) + 1 : undefined,
        deliveryAddress: orderType === "delivery" ? `عنوان التوصيل ${i + 1}` : undefined,
        notes: Math.random() > 0.7 ? `ملاحظة للطلب ${i + 1}` : "",
        createdBy: "admin",
        completedAt: statuses[Math.floor(Math.random() * statuses.length)] === "delivered" ? orderDate : undefined,
      };

      // Set the created date for the order
      const savedOrder = this.saveOrder(sampleOrder);
      // Update the created date to the random date
      const orders = this.getOrders();
      const orderIndex = orders.findIndex(o => o.id === savedOrder.id);
      if (orderIndex !== -1) {
        orders[orderIndex].createdAt = orderDate;
        if (sampleOrder.completedAt) {
          orders[orderIndex].completedAt = orderDate;
        }
        this.saveToStorage('restaurant_orders', orders);
      }
    }
  }
}

// Export singleton instance
export const db = RestaurantDatabase.getInstance();
