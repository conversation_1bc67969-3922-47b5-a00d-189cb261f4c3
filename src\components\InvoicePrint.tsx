import React from "react";
import { Order, db } from "@/lib/database";
import { format } from "date-fns";

interface InvoicePrintProps {
  order: Order;
  onClose: () => void;
}

const InvoicePrint: React.FC<InvoicePrintProps> = ({ order, onClose }) => {
  const settings = db.getSettings();
  const menuItems = db.getMenuItems();

  const handlePrint = () => {
    try {
      const printContent = `
        <html dir="rtl">
          <head>
            <title>فاتورة رقم ${order.orderNumber}</title>
            <meta charset="UTF-8">
            <style>
              @media print {
                body { margin: 0; }
                .no-print { display: none !important; }
                @page { margin: 1cm; }
              }
              body { 
                font-family: 'Arial', sans-serif; 
                margin: 0;
                padding: 20px;
                direction: rtl; 
                color: #333;
                line-height: 1.4;
                font-size: 14px;
              }
              .invoice-container {
                max-width: 800px;
                margin: 0 auto;
                background: white;
              }
              .header { 
                text-align: center; 
                border-bottom: 2px solid #333; 
                padding-bottom: 20px; 
                margin-bottom: 30px; 
              }
              .header h1 { 
                margin: 0; 
                color: #2c3e50; 
                font-size: 28px;
              }
              .header .subtitle { 
                margin: 5px 0; 
                color: #7f8c8d; 
                font-size: 16px;
              }
              .header .contact {
                margin-top: 10px;
                font-size: 12px;
                color: #666;
              }
              .invoice-info {
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 30px;
                margin-bottom: 30px;
                padding: 20px;
                background: #f8f9fa;
                border-radius: 8px;
              }
              .info-section h3 {
                margin: 0 0 10px 0;
                color: #2c3e50;
                font-size: 16px;
                border-bottom: 1px solid #ddd;
                padding-bottom: 5px;
              }
              .info-row {
                display: flex;
                justify-content: space-between;
                margin: 8px 0;
                font-size: 14px;
              }
              .info-label {
                font-weight: bold;
                color: #555;
              }
              .items-table {
                width: 100%;
                border-collapse: collapse;
                margin: 20px 0;
                background: white;
              }
              .items-table th {
                background: #34495e;
                color: white;
                padding: 12px 8px;
                text-align: right;
                font-weight: bold;
                border: 1px solid #2c3e50;
              }
              .items-table td {
                padding: 10px 8px;
                border: 1px solid #ddd;
                text-align: right;
              }
              .items-table tr:nth-child(even) {
                background: #f8f9fa;
              }
              .totals {
                margin-top: 30px;
                padding: 20px;
                background: #f8f9fa;
                border-radius: 8px;
              }
              .total-row {
                display: flex;
                justify-content: space-between;
                margin: 8px 0;
                padding: 5px 0;
                font-size: 16px;
              }
              .total-row.final {
                border-top: 2px solid #333;
                padding-top: 10px;
                font-weight: bold;
                font-size: 20px;
                color: #2c3e50;
              }
              .footer {
                text-align: center;
                margin-top: 40px;
                padding-top: 20px;
                border-top: 1px solid #ddd;
                color: #666;
                font-size: 12px;
              }
              .status-badge {
                display: inline-block;
                padding: 4px 12px;
                border-radius: 20px;
                font-size: 12px;
                font-weight: bold;
                text-transform: uppercase;
              }
              .status-delivered { background: #d4edda; color: #155724; }
              .status-pending { background: #fff3cd; color: #856404; }
              .status-preparing { background: #cce7ff; color: #004085; }
              .status-ready { background: #d1ecf1; color: #0c5460; }
              .status-cancelled { background: #f8d7da; color: #721c24; }
            </style>
          </head>
          <body>
            <div class="invoice-container">
              <div class="header">
                <h1>${settings?.name || "مطعم الذواقة"}</h1>
                <div class="subtitle">فاتورة مبيعات</div>
                <div class="contact">
                  ${settings?.address || ""}<br>
                  هاتف: ${settings?.phone || ""} | إيميل: ${settings?.email || ""}
                </div>
              </div>
              
              <div class="invoice-info">
                <div class="info-section">
                  <h3>معلومات الفاتورة</h3>
                  <div class="info-row">
                    <span class="info-label">رقم الفاتورة:</span>
                    <span>${order.orderNumber}</span>
                  </div>
                  <div class="info-row">
                    <span class="info-label">تاريخ الإنشاء:</span>
                    <span>${format(order.createdAt, "dd/MM/yyyy")}</span>
                  </div>
                  <div class="info-row">
                    <span class="info-label">وقت الإنشاء:</span>
                    <span>${format(order.createdAt, "HH:mm:ss")}</span>
                  </div>
                  <div class="info-row">
                    <span class="info-label">حالة الطلب:</span>
                    <span class="status-badge status-${order.status}">
                      ${order.status === "pending" ? "في الانتظار" :
                        order.status === "preparing" ? "قيد التحضير" :
                        order.status === "ready" ? "جاهز" :
                        order.status === "delivered" ? "تم التسليم" : "ملغي"}
                    </span>
                  </div>
                </div>
                
                <div class="info-section">
                  <h3>معلومات العميل</h3>
                  <div class="info-row">
                    <span class="info-label">اسم العميل:</span>
                    <span>${order.customerName}</span>
                  </div>
                  ${order.customerPhone ? `
                  <div class="info-row">
                    <span class="info-label">رقم الهاتف:</span>
                    <span>${order.customerPhone}</span>
                  </div>
                  ` : ''}
                  <div class="info-row">
                    <span class="info-label">نوع الطلب:</span>
                    <span>${order.orderType === "dine-in" ? "تناول في المطعم" :
                           order.orderType === "delivery" ? "توصيل" : "سفري"}</span>
                  </div>
                  ${order.tableNumber ? `
                  <div class="info-row">
                    <span class="info-label">رقم الطاولة:</span>
                    <span>${order.tableNumber}</span>
                  </div>
                  ` : ''}
                  ${order.deliveryAddress ? `
                  <div class="info-row">
                    <span class="info-label">عنوان التوصيل:</span>
                    <span>${order.deliveryAddress}</span>
                  </div>
                  ` : ''}
                  <div class="info-row">
                    <span class="info-label">طريقة الدفع:</span>
                    <span>${order.paymentMethod === "cash" ? "نقدي" :
                           order.paymentMethod === "card" ? "بطاقة" : "أونلاين"}</span>
                  </div>
                </div>
              </div>
              
              <table class="items-table">
                <thead>
                  <tr>
                    <th>العنصر</th>
                    <th>الكمية</th>
                    <th>السعر الواحد</th>
                    <th>المجموع</th>
                  </tr>
                </thead>
                <tbody>
                  ${order.items.map(item => {
                    const menuItem = menuItems.find(mi => mi.id === item.menuItemId);
                    return `
                    <tr>
                      <td>${menuItem?.name || "عنصر غير معروف"}</td>
                      <td>${item.quantity}</td>
                      <td>${item.unitPrice.toFixed(2)} ${settings?.currencySymbol || "ر.س"}</td>
                      <td>${item.totalPrice.toFixed(2)} ${settings?.currencySymbol || "ر.س"}</td>
                    </tr>
                    `;
                  }).join('')}
                </tbody>
              </table>
              
              ${order.notes ? `
              <div style="margin: 20px 0; padding: 15px; background: #fff3cd; border-radius: 5px;">
                <strong>ملاحظات:</strong> ${order.notes}
              </div>
              ` : ''}
              
              <div class="totals">
                <div class="total-row">
                  <span>المجموع الفرعي:</span>
                  <span>${order.subtotal.toFixed(2)} ${settings?.currencySymbol || "ر.س"}</span>
                </div>
                ${order.discount > 0 ? `
                <div class="total-row">
                  <span>الخصم:</span>
                  <span>-${order.discount.toFixed(2)} ${settings?.currencySymbol || "ر.س"}</span>
                </div>
                ` : ''}
                <div class="total-row">
                  <span>الضريبة (${((order.tax / order.subtotal) * 100).toFixed(0)}%):</span>
                  <span>${order.tax.toFixed(2)} ${settings?.currencySymbol || "ر.س"}</span>
                </div>
                <div class="total-row final">
                  <span>المجموع الكلي:</span>
                  <span>${order.total.toFixed(2)} ${settings?.currencySymbol || "ر.س"}</span>
                </div>
              </div>
              
              <div class="footer">
                <p><strong>شكراً لزيارتكم ${settings?.name || "مطعمنا"}</strong></p>
                <p>تم إنشاء هذه الفاتورة في: ${format(new Date(), "dd/MM/yyyy HH:mm:ss")}</p>
                ${order.status === "delivered" && order.completedAt ? 
                  `<p>تم إكمال الطلب في: ${format(order.completedAt, "dd/MM/yyyy HH:mm:ss")}</p>` : ''}
              </div>
            </div>
          </body>
        </html>
      `;

      // Open print window
      const printWindow = window.open("", "_blank", "width=800,height=600");
      
      if (printWindow) {
        printWindow.document.write(printContent);
        printWindow.document.close();
        
        // Wait for content to load then print
        printWindow.onload = () => {
          setTimeout(() => {
            printWindow.print();
            // Close window after printing
            setTimeout(() => {
              printWindow.close();
              onClose(); // Close the dialog
            }, 1000);
          }, 500);
        };
      } else {
        alert("تعذر فتح نافذة الطباعة. يرجى التأكد من عدم حظر النوافذ المنبثقة.");
      }
    } catch (error) {
      console.error("خطأ في طباعة الفاتورة:", error);
      alert("حدث خطأ أثناء طباعة الفاتورة. يرجى المحاولة مرة أخرى.");
    }
  };

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
        <h3 className="text-lg font-bold mb-4">طباعة الفاتورة</h3>
        <p className="text-sm text-muted-foreground mb-6">
          هل تريد طباعة فاتورة الطلب رقم {order.orderNumber}؟
        </p>
        <div className="flex gap-3 justify-end">
          <button
            onClick={onClose}
            className="px-4 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50"
          >
            إلغاء
          </button>
          <button
            onClick={handlePrint}
            className="px-4 py-2 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            طباعة
          </button>
        </div>
      </div>
    </div>
  );
};

export default InvoicePrint;
