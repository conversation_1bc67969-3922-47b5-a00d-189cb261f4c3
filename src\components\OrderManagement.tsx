import React, { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent } from "@/components/ui/tabs";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  ChevronRight,
  Plus,
  Trash2,
  CreditCard,
  Receipt,
  Clock,
  CheckCircle,
  AlertCircle,
  Printer,
  Settings,
} from "lucide-react";
import ProductCatalog from "./ProductCatalog";
import OrderDetails from "./OrderDetails";

interface OrderItem {
  id: string;
  name: string;
  price: number;
  quantity: number;
}

interface Order {
  id: string;
  type: "dine-in" | "delivery" | "takeaway";
  tableNumber?: number;
  customerName?: string;
  customerPhone?: string;
  customerAddress?: string;
  items: OrderItem[];
  status: "new" | "in-progress" | "completed";
  total: number;
  createdAt: Date;
}

interface OrderManagementProps {
  language?: "english" | "arabic";
  userType?: "admin" | "cashier";
}

const OrderManagement = ({
  language = "arabic",
  userType = "cashier",
}: OrderManagementProps) => {
  const [activeTab, setActiveTab] = useState<"dine-in" | "delivery" | "takeaway">("dine-in");
  const [selectedTable, setSelectedTable] = useState<number | null>(null);
  const [customerInfo, setCustomerInfo] = useState({
    name: "",
    phone: "",
    address: "",
  });
  const [currentOrder, setCurrentOrder] = useState<Order | null>(null);
  const [tableCount, setTableCount] = useState(10);
  const [tableStartTimes, setTableStartTimes] = useState<{[key: number]: Date}>({});
  const [isEditTableOpen, setIsEditTableOpen] = useState(false);
  const [editingTableNumber, setEditingTableNumber] = useState<number | null>(null);
  const [orders, setOrders] = useState<Order[]>([
    {
      id: "1",
      type: "dine-in",
      tableNumber: 1,
      items: [
        { id: "101", name: "Burger", price: 8.99, quantity: 2 },
        { id: "102", name: "Fries", price: 3.99, quantity: 1 },
      ],
      status: "in-progress",
      total: 21.97,
      createdAt: new Date(),
    },
    {
      id: "2",
      type: "delivery",
      customerName: "John Doe",
      customerPhone: "555-1234",
      customerAddress: "123 Main St",
      items: [{ id: "103", name: "Pizza", price: 12.99, quantity: 1 }],
      status: "new",
      total: 12.99,
      createdAt: new Date(),
    },
  ]);

  const tables = Array.from({ length: tableCount }, (_, i) => i + 1);
  const [selectedCategory, setSelectedCategory] = useState("جميع الفئات");
  const [newOrder, setNewOrder] = useState({
    customerName: "",
    customerPhone: "",
    orderType: "dine-in" as "dine-in" | "delivery" | "takeaway",
    paymentMethod: "cash" as "cash" | "card" | "online",
    tableNumber: "",
    deliveryAddress: "",
    notes: "",
    items: [] as any[],
  });

  // Complete menu items - 16 products
  const sampleMenuItems = [
    // أطباق رئيسية
    { id: "1", name: "كبسة دجاج", price: 45, category: "أطباق رئيسية", description: "أرز بسمتي مع دجاج مشوي وخلطة البهارات الخاصة" },
    { id: "2", name: "مندي لحم", price: 65, category: "أطباق رئيسية", description: "لحم خروف طري مع أرز مندي مدخن" },
    { id: "3", name: "مشاوي مشكلة", price: 85, category: "أطباق رئيسية", description: "تشكيلة من اللحوم المشوية مع الخضار" },
    { id: "4", name: "فراخ مشوية", price: 38, category: "أطباق رئيسية", description: "دجاج كامل مشوي مع البهارات والأعشاب" },

    // مقبلات
    { id: "5", name: "حمص بالطحينة", price: 18, category: "مقبلات", description: "حمص كريمي مع الطحينة وزيت الزيتون" },
    { id: "6", name: "فتوش", price: 22, category: "مقبلات", description: "سلطة خضار مشكلة مع الخبز المحمص" },
    { id: "7", name: "تبولة", price: 20, category: "مقبلات", description: "سلطة البقدونس مع الطماطم والبرغل" },
    { id: "8", name: "بابا غنوج", price: 16, category: "مقبلات", description: "باذنجان مشوي مع الطحينة والثوم" },

    // مشروبات
    { id: "9", name: "عصير برتقال طازج", price: 12, category: "مشروبات", description: "عصير برتقال طبيعي 100%" },
    { id: "10", name: "شاي أحمر", price: 8, category: "مشروبات", description: "شاي أحمر تقليدي مع السكر" },
    { id: "11", name: "قهوة عربية", price: 10, category: "مشروبات", description: "قهوة عربية أصيلة مع الهيل" },
    { id: "12", name: "عصير مانجو", price: 15, category: "مشروبات", description: "عصير مانجو طازج ومنعش" },

    // حلويات
    { id: "13", name: "كنافة نابلسية", price: 25, category: "حلويات", description: "كنافة محشوة بالجبن مع القطر" },
    { id: "14", name: "مهلبية", price: 15, category: "حلويات", description: "حلى كريمي بالحليب مع ماء الورد" },
    { id: "15", name: "بقلاوة", price: 20, category: "حلويات", description: "حلوى شرقية بالعجين الرقيق والمكسرات" },
    { id: "16", name: "أم علي", price: 18, category: "حلويات", description: "حلى ساخن بالحليب والمكسرات والزبيب" }
  ];

  // Database simulation
  const saveToDatabase = (order: Order) => {
    try {
      const existingOrders = JSON.parse(localStorage.getItem("restaurant_orders") || "[]");
      const updatedOrders = [...existingOrders, { ...order, id: `order-${Date.now()}` }];
      localStorage.setItem("restaurant_orders", JSON.stringify(updatedOrders));
      console.log("Order saved to database:", order);
    } catch (error) {
      console.error("Error saving to database:", error);
    }
  };

  const loadFromDatabase = () => {
    try {
      const savedOrders = JSON.parse(localStorage.getItem("restaurant_orders") || "[]");
      setOrders(savedOrders);
    } catch (error) {
      console.error("Error loading from database:", error);
    }
  };

  useEffect(() => {
    loadFromDatabase();
  }, []);

  const handleTableSelect = (tableNumber: number) => {
    setSelectedTable(tableNumber);
    const existingOrder = orders.find(
      (order) => order.type === "dine-in" && order.tableNumber === tableNumber && order.status !== "completed"
    );

    if (existingOrder) {
      setCurrentOrder(existingOrder);
    } else {
      const newOrder: Order = {
        id: `order-${Date.now()}`,
        type: "dine-in",
        tableNumber: tableNumber,
        items: [],
        status: "new",
        total: 0,
        createdAt: new Date(),
      };
      setCurrentOrder(newOrder);
      setTableStartTimes(prev => ({ ...prev, [tableNumber]: new Date() }));
    }
  };

  const getTableDuration = (tableNumber: number) => {
    const startTime = tableStartTimes[tableNumber];
    if (!startTime) return null;
    const now = new Date();
    const diffMs = now.getTime() - startTime.getTime();
    const diffMins = Math.floor(diffMs / 60000);
    const hours = Math.floor(diffMins / 60);
    const minutes = diffMins % 60;
    if (hours > 0) {
      return `${hours}س ${minutes}د`;
    }
    return `${minutes}د`;
  };

  const handleCustomerInfoChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setCustomerInfo((prev) => ({ ...prev, [name]: value }));
  };

  const createDeliveryOrder = () => {
    if (!customerInfo.name || !customerInfo.phone || !customerInfo.address) {
      return;
    }
    const newOrder: Order = {
      id: `order-${Date.now()}`,
      type: "delivery",
      customerName: customerInfo.name,
      customerPhone: customerInfo.phone,
      customerAddress: customerInfo.address,
      items: [],
      status: "new",
      total: 0,
      createdAt: new Date(),
    };
    setCurrentOrder(newOrder);
  };

  const createTakeawayOrder = () => {
    const newOrder: Order = {
      id: `order-${Date.now()}`,
      type: "takeaway",
      items: [],
      status: "new",
      total: 0,
      createdAt: new Date(),
    };
    setCurrentOrder(newOrder);
  };

  const addItemToOrder = (item: { id: string; name: string; price: number; }) => {
    if (!currentOrder) return;
    setCurrentOrder((prevOrder) => {
      if (!prevOrder) return null;
      const existingItem = prevOrder.items.find((i) => i.id === item.id);
      let updatedItems;
      if (existingItem) {
        updatedItems = prevOrder.items.map((i) =>
          i.id === item.id ? { ...i, quantity: i.quantity + 1 } : i
        );
      } else {
        updatedItems = [...prevOrder.items, { ...item, quantity: 1 }];
      }
      const total = updatedItems.reduce((sum, item) => sum + item.price * item.quantity, 0);
      return { ...prevOrder, items: updatedItems, total };
    });
  };

  const saveOrder = () => {
    if (!currentOrder || currentOrder.items.length === 0) return;
    const completedOrder = { ...currentOrder, status: "completed" as const };
    saveToDatabase(completedOrder);
    setOrders((prevOrders) => {
      const orderExists = prevOrders.some((order) => order.id === currentOrder.id);
      if (orderExists) {
        return prevOrders.map((order) => order.id === currentOrder.id ? completedOrder : order);
      } else {
        return [...prevOrders, completedOrder];
      }
    });
    setSelectedTable(null);
    setCustomerInfo({ name: "", phone: "", address: "" });
    setCurrentOrder(null);
  };

  const updateOrderStatus = (orderId: string, status: string) => {
    setOrders((prevOrders) =>
      prevOrders.map((order) => order.id === orderId ? { ...order, status: status as any } : order)
    );
  };

  const printOrder = (order: Order) => {
    console.log("Printing order:", order);
    alert("تم إرسال الطلب للطباعة");
  };

  const prepareOrder = (order: Order) => {
    console.log("Preparing order for kitchen:", order);
    alert("تم إرسال الطلب للمطبخ للتجهيز");
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "new":
        return <Badge variant="outline" className="bg-blue-100 text-blue-800">New</Badge>;
      case "in-progress":
        return <Badge variant="outline" className="bg-yellow-100 text-yellow-800">In Progress</Badge>;
      case "completed":
        return <Badge variant="outline" className="bg-green-100 text-green-800">Completed</Badge>;
      default:
        return null;
    }
  };

  return (
    <div className="flex flex-col h-full" style={{ backgroundColor: '#f8f9fa' }}>
      {/* Enhanced Header */}
      <div
        className="relative overflow-hidden"
        style={{
          background: 'linear-gradient(135deg, #6c757d 0%, #495057 50%, #343a40 100%)',
          borderBottom: '3px solid #dee2e6'
        }}
      >
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute top-0 left-0 w-full h-full"
               style={{
                 backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
                 backgroundSize: '30px 30px'
               }}
          ></div>
        </div>

        <div className="relative p-8">
          <div className="flex justify-between items-center">
            <div className="flex items-center gap-6">
              {/* Enhanced Avatar */}
              <div className="relative">
                <div
                  className="w-16 h-16 rounded-full flex items-center justify-center shadow-lg border-4 border-white/20"
                  style={{
                    background: userType === "admin"
                      ? 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
                      : 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
                  }}
                >
                  <span className="text-2xl">
                    {userType === "admin" ? "👨‍💼" : "👨‍💻"}
                  </span>
                </div>
                <div className="absolute -bottom-1 -right-1 w-6 h-6 rounded-full bg-green-400 border-2 border-white flex items-center justify-center">
                  <div className="w-2 h-2 rounded-full bg-white"></div>
                </div>
              </div>

              <div>
                <h1 className="text-3xl font-bold text-white mb-2 font-elegant">
                  {userType === "admin" ? "🏢 لوحة تحكم المدير" : "🍽️ نظام إدارة الطلبات"}
                </h1>
                <p className="text-white/80 text-lg font-elegant">
                  {userType === "admin"
                    ? "مراقبة شاملة وتحليل الأداء"
                    : "إدارة متقدمة للطلبات والطاولات"}
                </p>
                <div className="flex items-center gap-4 mt-2">
                  <div className="flex items-center gap-2 text-white/70">
                    <div className="w-2 h-2 rounded-full bg-green-400"></div>
                    <span className="text-sm font-elegant">متصل</span>
                  </div>
                  <div className="text-white/70 text-sm font-elegant">
                    آخر تحديث: {new Date().toLocaleTimeString('ar-SA')}
                  </div>
                </div>
              </div>
            </div>

            {/* Enhanced Status Cards */}
            <div className="flex items-center gap-4">
              <div
                className="px-6 py-4 rounded-xl shadow-lg backdrop-blur-sm border border-white/20"
                style={{
                  background: userType === "admin"
                    ? 'rgba(255, 255, 255, 0.15)'
                    : 'rgba(255, 255, 255, 0.15)'
                }}
              >
                <div className="text-center">
                  <div className="text-2xl mb-1">
                    {userType === "admin" ? "📊" : "⚡"}
                  </div>
                  <div className="text-white font-bold text-lg font-elegant">
                    {userType === "admin" ? "مراقب" : "نشط"}
                  </div>
                  <div className="text-white/70 text-sm font-elegant">
                    {userType === "admin" ? "وضع العرض" : "وضع التشغيل"}
                  </div>
                </div>
              </div>

              <div
                className="px-6 py-4 rounded-xl shadow-lg backdrop-blur-sm border border-white/20"
                style={{ background: 'rgba(255, 255, 255, 0.15)' }}
              >
                <div className="text-center">
                  <div className="text-2xl mb-1">📅</div>
                  <div className="text-white font-bold text-lg font-elegant">
                    {new Date().toLocaleDateString('ar-SA', {
                      weekday: 'short',
                      day: 'numeric',
                      month: 'short'
                    })}
                  </div>
                  <div className="text-white/70 text-sm font-elegant">
                    {new Date().toLocaleTimeString('ar-SA', {
                      hour: '2-digit',
                      minute: '2-digit'
                    })}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="flex flex-1 overflow-hidden">
        {/* Enhanced Left Panel - Order Type Selection (Cashier Only) */}
        {userType === "cashier" && (
          <div
            className="w-1/4 border-r shadow-lg"
            style={{
              background: 'linear-gradient(180deg, #ffffff 0%, #f8f9fa 100%)',
              borderColor: '#e9ecef'
            }}
          >
            {/* Section Header */}
            <div className="p-6 border-b" style={{ borderColor: '#e9ecef' }}>
              <div className="text-center mb-4">
                <div
                  className="w-12 h-12 mx-auto rounded-full flex items-center justify-center mb-3 shadow-md"
                  style={{
                    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                    color: 'white'
                  }}
                >
                  <span className="text-xl">🍽️</span>
                </div>
                <h3 className="text-xl font-bold font-elegant mb-2" style={{ color: '#343a40' }}>
                  أنواع الطلبات
                </h3>
                <p className="text-sm" style={{ color: '#6c757d' }}>
                  اختر نوع الطلب لبدء العمل
                </p>
              </div>
            </div>

            <div className="p-6">

            <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as any)}>
              {/* Enhanced Tab List */}
              <div className="grid grid-cols-1 gap-3 mb-6">
                {[
                  { value: "dine-in", icon: "🍽️", title: "تناول في المطعم", desc: "طلبات الطاولات" },
                  { value: "delivery", icon: "🚚", title: "توصيل", desc: "طلبات التوصيل" },
                  { value: "takeaway", icon: "📦", title: "سفري", desc: "طلبات الاستلام" }
                ].map((tab) => (
                  <button
                    key={tab.value}
                    onClick={() => setActiveTab(tab.value as any)}
                    className={`p-4 rounded-xl text-right transition-all duration-300 border-2 ${
                      activeTab === tab.value
                        ? 'shadow-lg transform scale-105'
                        : 'hover:shadow-md hover:transform hover:scale-102'
                    }`}
                    style={{
                      background: activeTab === tab.value
                        ? 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
                        : 'linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%)',
                      borderColor: activeTab === tab.value ? '#667eea' : '#e9ecef',
                      color: activeTab === tab.value ? 'white' : '#495057'
                    }}
                  >
                    <div className="flex items-center gap-3">
                      <div className={`text-2xl ${activeTab === tab.value ? 'animate-pulse' : ''}`}>
                        {tab.icon}
                      </div>
                      <div>
                        <div className="font-bold font-elegant text-lg">{tab.title}</div>
                        <div className={`text-sm font-elegant ${
                          activeTab === tab.value ? 'text-white/80' : 'text-gray-500'
                        }`}>
                          {tab.desc}
                        </div>
                      </div>
                    </div>
                  </button>
                ))}
              </div>

              <TabsContent value="dine-in" className="mt-6 flex flex-col h-full">
                {/* Enhanced Tables Section */}
                <div className="mb-6 flex-shrink-0">
                  <div className="text-center mb-4">
                    <div
                      className="w-10 h-10 mx-auto rounded-full flex items-center justify-center mb-2 shadow-md"
                      style={{
                        background: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
                        color: 'white'
                      }}
                    >
                      <span className="text-lg">🪑</span>
                    </div>
                    <h4 className="text-lg font-bold font-elegant mb-1" style={{ color: '#343a40' }}>
                      اختر الطاولة
                    </h4>
                    <p className="text-sm" style={{ color: '#6c757d' }}>
                      الطاولات الخضراء متاحة، الحمراء مشغولة
                    </p>
                  </div>

                  {/* Tables Grid */}
                  <div className="grid grid-cols-2 gap-4">
                    {tables.map((table) => {
                      const isOccupied = orders.some(
                        (o) => o.type === "dine-in" && o.tableNumber === table && o.status !== "completed"
                      );

                      return (
                        <div key={table} className="space-y-2">
                          {/* Enhanced Table Card */}
                          <div
                            className={`relative p-4 rounded-xl border-2 cursor-pointer transition-all duration-300 ${
                              selectedTable === table
                                ? 'shadow-xl transform scale-105 border-blue-400'
                                : 'hover:shadow-lg hover:transform hover:scale-102'
                            }`}
                            style={{
                              background: selectedTable === table
                                ? 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
                                : isOccupied
                                ? 'linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%)'
                                : 'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)',
                              borderColor: selectedTable === table
                                ? '#667eea'
                                : isOccupied
                                ? '#ff6b6b'
                                : '#51cf66'
                            }}
                            onClick={() => handleTableSelect(table)}
                          >
                            {/* Status Indicator */}
                            <div className="absolute top-2 right-2 flex items-center gap-1">
                              <div className={`w-3 h-3 rounded-full ${
                                isOccupied ? "bg-red-500" : "bg-green-500"
                              } shadow-md`}></div>
                              {isOccupied && (
                                <div className="w-3 h-3 rounded-full bg-yellow-400 animate-pulse"></div>
                              )}
                            </div>

                            {/* Table Content */}
                            <div className="text-center">
                              <div className={`text-3xl mb-2 ${selectedTable === table ? 'animate-bounce' : ''}`}>
                                🪑
                              </div>
                              <div className={`font-bold text-lg font-elegant mb-1 ${
                                selectedTable === table ? 'text-white' : 'text-gray-700'
                              }`}>
                                طاولة {table}
                              </div>
                              <div className={`text-sm font-elegant mb-2 ${
                                selectedTable === table ? 'text-white/80' : 'text-gray-600'
                              }`}>
                                {isOccupied ? "🔴 مشغولة" : "🟢 متاحة"}
                              </div>

                              {/* Timer Display */}
                              {isOccupied && getTableDuration(table) && (
                                <div className={`text-xs font-mono px-2 py-1 rounded-full ${
                                  selectedTable === table
                                    ? 'bg-white/20 text-white'
                                    : 'bg-gray-700/20 text-gray-700'
                                }`}>
                                  ⏱️ {getTableDuration(table)}
                                </div>
                              )}
                            </div>
                          </div>
                          {/* Enhanced Action Buttons */}
                          {isOccupied && (
                            <div className="space-y-2 mt-3">
                              <div className="grid grid-cols-2 gap-2">
                                <button
                                  className="px-3 py-2 rounded-lg text-xs font-bold font-elegant transition-all duration-200 shadow-md hover:shadow-lg transform hover:scale-105"
                                  style={{
                                    background: 'linear-gradient(135deg, #ff9a56 0%, #ff6b6b 100%)',
                                    color: 'white',
                                    border: 'none'
                                  }}
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    const tableOrder = orders.find(
                                      (o) => o.type === "dine-in" && o.tableNumber === table && o.status !== "completed"
                                    );
                                    if (tableOrder) {
                                      prepareOrder(tableOrder);
                                    }
                                  }}
                                >
                                  🍳 تجهيز
                                </button>
                                <button
                                  className="px-3 py-2 rounded-lg text-xs font-bold font-elegant transition-all duration-200 shadow-md hover:shadow-lg transform hover:scale-105"
                                  style={{
                                    background: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
                                    color: 'white',
                                    border: 'none'
                                  }}
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    const tableOrder = orders.find(
                                      (o) => o.type === "dine-in" && o.tableNumber === table && o.status !== "completed"
                                    );
                                    if (tableOrder) {
                                      const completedOrder = { ...tableOrder, status: "completed" as const };
                                      saveToDatabase(completedOrder);
                                      setOrders((prevOrders) =>
                                        prevOrders.map((order) =>
                                          order.id === tableOrder.id ? { ...order, status: "completed" as const } : order
                                        )
                                      );
                                      if (currentOrder?.tableNumber === table) {
                                        setCurrentOrder(null);
                                      }
                                      if (selectedTable === table) {
                                        setSelectedTable(null);
                                      }
                                      setTableStartTimes(prev => {
                                        const newTimes = { ...prev };
                                        delete newTimes[table];
                                        return newTimes;
                                      });
                                    }
                                  }}
                                >
                                  💳 دفع
                                </button>
                              </div>
                              <button
                                className="w-full px-3 py-2 rounded-lg text-xs font-bold font-elegant transition-all duration-200 shadow-md hover:shadow-lg transform hover:scale-105"
                                style={{
                                  background: 'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)',
                                  color: '#495057',
                                  border: 'none'
                                }}
                                onClick={(e) => {
                                  e.stopPropagation();
                                  setEditingTableNumber(table);
                                  setIsEditTableOpen(true);
                                }}
                              >
                                ✏️ تعديل الطاولة
                              </button>
                            </div>
                          )}
                        </div>
                      );
                    })}
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="delivery" className="mt-4">
                <Card>
                  <CardContent className="pt-4">
                    <div className="space-y-4">
                      <div className="space-y-2">
                        <Label htmlFor="name">اسم العميل</Label>
                        <Input
                          id="name"
                          name="name"
                          value={customerInfo.name}
                          onChange={handleCustomerInfoChange}
                          placeholder="أدخل اسم العميل"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="phone">رقم الهاتف</Label>
                        <Input
                          id="phone"
                          name="phone"
                          value={customerInfo.phone}
                          onChange={handleCustomerInfoChange}
                          placeholder="أدخل رقم الهاتف"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="address">عنوان التوصيل</Label>
                        <Input
                          id="address"
                          name="address"
                          value={customerInfo.address}
                          onChange={handleCustomerInfoChange}
                          placeholder="أدخل عنوان التوصيل"
                        />
                      </div>
                      <Button
                        className="w-full"
                        onClick={createDeliveryOrder}
                        disabled={!customerInfo.name || !customerInfo.phone || !customerInfo.address}
                      >
                        إنشاء طلب توصيل
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="takeaway" className="mt-4">
                <Card>
                  <CardContent className="pt-4">
                    <div className="space-y-4">
                      <p>إنشاء طلب سفري جديد</p>
                      <Button className="w-full" onClick={createTakeawayOrder}>
                        إنشاء طلب سفري
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>

            <div className="my-6 h-px" style={{ backgroundColor: '#dee2e6' }}></div>

            <div>
              <div className="mb-4">
                <h4 className="text-md font-bold font-elegant mb-2" style={{ color: '#495057' }}>
                  📋 الطلبات النشطة
                </h4>
                <p className="text-xs" style={{ color: '#6c757d' }}>
                  الطلبات الحالية قيد التنفيذ
                </p>
              </div>
              <ScrollArea className="h-[300px]">
                <div className="space-y-2">
                  {orders
                    .filter((order) => order.status !== "completed")
                    .map((order) => (
                      <Card key={order.id} className="cursor-pointer hover:bg-accent/50">
                        <CardContent className="p-3">
                          <div className="flex justify-between items-center">
                            <div>
                              <div className="font-medium">
                                {order.type === "dine-in"
                                  ? `Table ${order.tableNumber}`
                                  : order.type === "delivery"
                                    ? `Delivery: ${order.customerName}`
                                    : "Takeaway"}
                              </div>
                              <div className="text-sm text-muted-foreground">
                                {new Date(order.createdAt).toLocaleTimeString("ar-SA")} • {order.items.length} عنصر
                              </div>
                            </div>
                            <div className="flex items-center gap-2">
                              {getStatusBadge(order.status)}
                              <ChevronRight className="h-4 w-4 text-muted-foreground" />
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                </div>
              </ScrollArea>
            </div>
          </div>
        )}

        {/* Middle Panel - Order Details */}
        <div className={userType === "cashier" ? "w-1/3 border-r" : "w-1/2 border-r"}>
          {userType === "admin" ? (
            <div className="p-8 h-full" style={{ backgroundColor: '#f8f9fa' }}>
              {/* Enhanced Admin Dashboard */}
              <div className="max-w-md mx-auto">
                {/* Welcome Section */}
                <div className="text-center mb-8">
                  <div className="relative mb-6">
                    <div
                      className="w-24 h-24 mx-auto rounded-full flex items-center justify-center shadow-xl border-4 border-white"
                      style={{
                        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                        color: 'white'
                      }}
                    >
                      <span className="text-3xl">👨‍💼</span>
                    </div>
                    <div className="absolute -bottom-2 -right-2 w-8 h-8 rounded-full bg-green-400 border-4 border-white flex items-center justify-center shadow-lg">
                      <div className="w-3 h-3 rounded-full bg-white animate-pulse"></div>
                    </div>
                  </div>
                  <h3 className="text-2xl font-bold font-elegant mb-2" style={{ color: '#343a40' }}>
                    🏢 لوحة تحكم المدير
                  </h3>
                  <p className="text-lg font-elegant" style={{ color: '#6c757d' }}>
                    إدارة شاملة ومراقبة متقدمة
                  </p>
                </div>

                {/* Enhanced Feature Cards */}
                <div className="space-y-4 mb-8">
                  {[
                    {
                      icon: "📊",
                      title: "تقارير المبيعات",
                      desc: "تحليل مفصل مع بحث بالتاريخ والوقت",
                      gradient: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
                      action: "عرض التقارير"
                    },
                    {
                      icon: "🍽️",
                      title: "إدارة المخزون",
                      desc: "تعديل وإضافة المنتجات والأسعار",
                      gradient: "linear-gradient(135deg, #f093fb 0%, #f5576c 100%)",
                      action: "إدارة المنتجات"
                    },
                    {
                      icon: "👁️",
                      title: "مراقبة الطلبات",
                      desc: "عرض مباشر للطلبات النشطة",
                      gradient: "linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)",
                      action: "مراقبة مباشرة"
                    }
                  ].map((feature, index) => (
                    <div
                      key={index}
                      className="p-6 rounded-xl shadow-lg border-2 border-gray-200 hover:shadow-xl transition-all duration-300 cursor-pointer transform hover:scale-105"
                      style={{
                        background: 'linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%)',
                      }}
                    >
                      <div className="flex items-center gap-4">
                        <div
                          className="w-12 h-12 rounded-full flex items-center justify-center shadow-md"
                          style={{ background: feature.gradient, color: 'white' }}
                        >
                          <span className="text-xl">{feature.icon}</span>
                        </div>
                        <div className="flex-1">
                          <h4 className="font-bold font-elegant text-lg mb-1" style={{ color: '#343a40' }}>
                            {feature.title}
                          </h4>
                          <p className="text-sm font-elegant" style={{ color: '#6c757d' }}>
                            {feature.desc}
                          </p>
                        </div>
                        <div
                          className="px-4 py-2 rounded-lg text-sm font-bold font-elegant text-white shadow-md"
                          style={{ background: feature.gradient }}
                        >
                          {feature.action}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>

                {/* Enhanced Tip Section */}
                <div
                  className="p-6 rounded-xl shadow-lg border-2"
                  style={{
                    background: 'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)',
                    borderColor: '#4facfe'
                  }}
                >
                  <div className="flex items-center gap-3">
                    <div className="text-2xl">💡</div>
                    <div>
                      <div className="font-bold font-elegant text-lg" style={{ color: '#343a40' }}>
                        نصيحة سريعة
                      </div>
                      <div className="text-sm font-elegant" style={{ color: '#495057' }}>
                        استخدم تبويب "إدارة المخزون" لتعديل المنتجات والأسعار
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ) : currentOrder ? (
            <OrderDetails
              orderType={currentOrder.type}
              tableNumber={currentOrder.tableNumber}
              orderStatus={currentOrder.status}
              customerInfo={{
                name: currentOrder.customerName || "",
                phone: currentOrder.customerPhone || "",
                address: currentOrder.customerAddress || "",
              }}
              items={currentOrder.items}
              onUpdateStatus={(status) => updateOrderStatus(currentOrder.id, status)}
              onProcessPayment={() => saveOrder()}
              onUpdateQuantity={(itemId, quantity) => {
                setCurrentOrder((prev) => {
                  if (!prev) return null;
                  const updatedItems = prev.items.map((item) =>
                    item.id === itemId ? { ...item, quantity } : item
                  );
                  const total = updatedItems.reduce((sum, item) => sum + item.price * item.quantity, 0);
                  return { ...prev, items: updatedItems, total };
                });
              }}
              onRemoveItem={(itemId) => {
                setCurrentOrder((prev) => {
                  if (!prev) return null;
                  const updatedItems = prev.items.filter((item) => item.id !== itemId);
                  const total = updatedItems.reduce((sum, item) => sum + item.price * item.quantity, 0);
                  return { ...prev, items: updatedItems, total };
                });
              }}
              onPrintOrder={() => printOrder(currentOrder)}
              onPrepareOrder={() => prepareOrder(currentOrder)}
              language={language}
            />
          ) : (
            <div className="flex items-center justify-center h-full text-muted-foreground font-elegant">
              <div className="text-center">
                <div className="text-4xl mb-4">🍽️</div>
                <p>اختر طاولة أو أنشئ طلباً جديداً</p>
              </div>
            </div>
          )}
        </div>

        {/* Enhanced Right Panel - Product Catalog */}
        <div className={userType === "cashier" ? "w-5/12" : "w-1/2"} style={{ display: 'flex', flexDirection: 'column', height: '100%' }}>
          {userType === "admin" ? (
            <div className="flex flex-col h-full" style={{ backgroundColor: '#f8f9fa' }}>
              {/* Enhanced Admin Products Header */}
              <div className="p-6 flex-shrink-0 border-b" style={{ borderColor: '#e9ecef' }}>
                <div className="text-center">
                  <div
                    className="w-12 h-12 mx-auto rounded-full flex items-center justify-center mb-3 shadow-md"
                    style={{
                      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                      color: 'white'
                    }}
                  >
                    <span className="text-xl">📋</span>
                  </div>
                  <h3 className="text-xl font-bold font-elegant mb-2" style={{ color: '#343a40' }}>
                    عرض المنتجات
                  </h3>
                  <p className="text-sm font-elegant" style={{ color: '#6c757d' }}>
                    للتعديل، استخدم تبويب "إدارة المخزون"
                  </p>
                </div>
              </div>
              <div className="flex-1 overflow-hidden">
                <ProductCatalog
                  showInventoryControls={false}
                  onAddToOrder={() => {}}
                  language={language}
                />
              </div>
            </div>
          ) : (
            <div className="flex flex-col h-full" style={{ backgroundColor: '#f8f9fa' }}>
              {/* Enhanced Cashier Products Header */}
              <div className="p-6 flex-shrink-0 border-b" style={{ borderColor: '#e9ecef' }}>
                <div className="text-center">
                  <div
                    className="w-12 h-12 mx-auto rounded-full flex items-center justify-center mb-3 shadow-md"
                    style={{
                      background: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
                      color: 'white'
                    }}
                  >
                    <span className="text-xl">🛒</span>
                  </div>
                  <h3 className="text-xl font-bold font-elegant mb-2" style={{ color: '#343a40' }}>
                    كتالوج المنتجات
                  </h3>
                  <p className="text-sm font-elegant" style={{ color: '#6c757d' }}>
                    اختر المنتجات لإضافتها للطلب
                  </p>

                  {/* Quick Stats */}
                  <div className="flex justify-center gap-4 mt-4">
                    <div
                      className="px-3 py-2 rounded-lg shadow-sm"
                      style={{ backgroundColor: '#e3f2fd', color: '#1976d2' }}
                    >
                      <div className="text-xs font-elegant">المنتجات</div>
                      <div className="font-bold">16</div>
                    </div>
                    <div
                      className="px-3 py-2 rounded-lg shadow-sm"
                      style={{ backgroundColor: '#e8f5e8', color: '#388e3c' }}
                    >
                      <div className="text-xs font-elegant">الفئات</div>
                      <div className="font-bold">4</div>
                    </div>
                  </div>
                </div>
              </div>
              <div className="flex-1 overflow-hidden">
                <ProductCatalog
                  showInventoryControls={false}
                  onAddToOrder={addItemToOrder}
                  language={language}
                />
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Edit Table Dialog */}
      {isEditTableOpen && editingTableNumber && (
        <Dialog open={isEditTableOpen} onOpenChange={setIsEditTableOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>تعديل الطاولة {editingTableNumber}</DialogTitle>
            </DialogHeader>
            <div className="space-y-4">
              <p>سيتم إضافة خيارات تعديل الطاولة قريباً</p>
              <Button onClick={() => setIsEditTableOpen(false)}>
                إغلاق
              </Button>
            </div>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
};

export default OrderManagement;
