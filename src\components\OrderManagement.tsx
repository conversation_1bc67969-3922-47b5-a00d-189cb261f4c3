import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import ProductCatalog from './ProductCatalog';

interface OrderItem {
  id: string;
  name: string;
  price: number;
  quantity: number;
  category: string;
}

interface Order {
  id: string;
  type: 'dine-in' | 'delivery' | 'takeaway';
  tableNumber?: number;
  customerName: string;
  customerPhone: string;
  customerAddress?: string;
  items: OrderItem[];
  total: number;
  status: 'pending' | 'preparing' | 'ready' | 'completed';
  createdAt: Date;
  paymentMethod: 'cash' | 'card' | 'online';
  notes?: string;
}

interface OrderManagementProps {
  userType: 'admin' | 'cashier';
  language: 'ar' | 'en';
}

const OrderManagement: React.FC<OrderManagementProps> = ({ userType, language }) => {
  const [orders, setOrders] = useState<Order[]>([]);
  const [currentOrder, setCurrentOrder] = useState<Order | null>(null);
  const [activeTab, setActiveTab] = useState<'dine-in' | 'delivery' | 'takeaway'>('dine-in');
  const [selectedTable, setSelectedTable] = useState<number | null>(null);
  const [isEditTableOpen, setIsEditTableOpen] = useState(false);
  const [editingTableNumber, setEditingTableNumber] = useState<number | null>(null);
  const [tableStartTimes, setTableStartTimes] = useState<Record<number, Date>>({});
  const [showOpenTables, setShowOpenTables] = useState(false);
  const [editingOrder, setEditingOrder] = useState<Order | null>(null);

  const tables = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10];

  // Load orders from localStorage
  useEffect(() => {
    const savedOrders = localStorage.getItem('restaurant_orders');
    if (savedOrders) {
      const parsedOrders = JSON.parse(savedOrders).map((order: any) => ({
        ...order,
        createdAt: new Date(order.createdAt)
      }));
      setOrders(parsedOrders);
    }

    const savedTableTimes = localStorage.getItem('table_start_times');
    if (savedTableTimes) {
      const parsedTimes = JSON.parse(savedTableTimes);
      const convertedTimes: Record<number, Date> = {};
      Object.keys(parsedTimes).forEach(key => {
        convertedTimes[parseInt(key)] = new Date(parsedTimes[key]);
      });
      setTableStartTimes(convertedTimes);
    }
  }, []);

  const saveToDatabase = (order: Order) => {
    const existingOrders = JSON.parse(localStorage.getItem('restaurant_orders') || '[]');
    const updatedOrders = [...existingOrders, order];
    localStorage.setItem('restaurant_orders', JSON.stringify(updatedOrders));
  };

  const addItemToOrder = (item: any) => {
    if (!currentOrder) {
      const newOrder: Order = {
        id: Date.now().toString(),
        type: activeTab,
        tableNumber: activeTab === 'dine-in' ? selectedTable || undefined : undefined,
        customerName: '',
        customerPhone: '',
        items: [],
        total: 0,
        status: 'pending',
        createdAt: new Date(),
        paymentMethod: 'cash'
      };
      setCurrentOrder(newOrder);
    }

    setCurrentOrder(prev => {
      if (!prev) return null;
      
      const existingItem = prev.items.find(orderItem => orderItem.id === item.id);
      let updatedItems;
      
      if (existingItem) {
        updatedItems = prev.items.map(orderItem =>
          orderItem.id === item.id
            ? { ...orderItem, quantity: orderItem.quantity + 1 }
            : orderItem
        );
      } else {
        updatedItems = [...prev.items, { ...item, quantity: 1 }];
      }
      
      const total = updatedItems.reduce((sum, orderItem) => sum + (orderItem.price * orderItem.quantity), 0);
      
      return { ...prev, items: updatedItems, total };
    });
  };

  const handleTableSelect = (tableNumber: number) => {
    setSelectedTable(tableNumber);
    
    const existingOrder = orders.find(
      order => order.type === 'dine-in' && order.tableNumber === tableNumber && order.status !== 'completed'
    );
    
    if (existingOrder) {
      setCurrentOrder(existingOrder);
    } else {
      setCurrentOrder(null);
    }
  };

  const prepareOrder = (order: Order) => {
    console.log('🍳 إرسال للمطبخ:', order);
    alert(`تم إرسال طلب الطاولة ${order.tableNumber} للمطبخ! 🍳`);
  };

  const getTableDuration = (tableNumber: number): string | null => {
    const startTime = tableStartTimes[tableNumber];
    if (!startTime) return null;
    
    const now = new Date();
    const diffMs = now.getTime() - startTime.getTime();
    const hours = Math.floor(diffMs / (1000 * 60 * 60));
    const minutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));
    
    return `${hours}س ${minutes}د`;
  };

  const completeOrder = () => {
    if (!currentOrder) return;

    if (!currentOrder.customerName.trim()) {
      alert('يرجى إدخال اسم العميل');
      return;
    }

    if (currentOrder.items.length === 0) {
      alert('يرجى إضافة عناصر للطلب');
      return;
    }

    const completedOrder = { ...currentOrder, status: 'completed' as const };
    saveToDatabase(completedOrder);
    setOrders(prev => [...prev, completedOrder]);

    if (currentOrder.type === 'dine-in' && currentOrder.tableNumber) {
      setTableStartTimes(prev => ({
        ...prev,
        [currentOrder.tableNumber!]: new Date()
      }));
      localStorage.setItem('table_start_times', JSON.stringify({
        ...tableStartTimes,
        [currentOrder.tableNumber]: new Date()
      }));
    }

    setCurrentOrder(null);
    setSelectedTable(null);
    alert('تم حفظ الطلب بنجاح! 🎉');
  };

  // Get occupied tables
  const getOccupiedTables = () => {
    return orders.filter(order =>
      order.type === 'dine-in' &&
      order.status !== 'completed' &&
      order.tableNumber
    );
  };

  // Complete table payment
  const completeTablePayment = (tableNumber: number) => {
    const tableOrder = orders.find(
      (o) => o.type === "dine-in" && o.tableNumber === tableNumber && o.status !== "completed"
    );

    if (tableOrder) {
      const completedOrder = { ...tableOrder, status: "completed" as const };
      saveToDatabase(completedOrder);
      setOrders((prevOrders) =>
        prevOrders.map((order) =>
          order.id === tableOrder.id ? { ...order, status: "completed" as const } : order
        )
      );

      // Clear table
      if (currentOrder?.tableNumber === tableNumber) {
        setCurrentOrder(null);
      }
      if (selectedTable === tableNumber) {
        setSelectedTable(null);
      }
      setTableStartTimes(prev => {
        const newTimes = { ...prev };
        delete newTimes[tableNumber];
        localStorage.setItem('table_start_times', JSON.stringify(newTimes));
        return newTimes;
      });

      alert(`تم إتمام دفع الطاولة ${tableNumber} بنجاح! 💳`);
    }
  };

  // Edit order item quantity
  const updateOrderItemQuantity = (orderId: string, itemId: string, newQuantity: number) => {
    if (newQuantity <= 0) {
      removeOrderItem(orderId, itemId);
      return;
    }

    setOrders(prevOrders =>
      prevOrders.map(order => {
        if (order.id === orderId) {
          const updatedItems = order.items.map(item =>
            item.id === itemId ? { ...item, quantity: newQuantity } : item
          );
          const newTotal = updatedItems.reduce((sum, item) => sum + (item.price * item.quantity), 0);
          return { ...order, items: updatedItems, total: newTotal };
        }
        return order;
      })
    );

    // Update current order if it's the same
    if (currentOrder?.id === orderId) {
      const updatedItems = currentOrder.items.map(item =>
        item.id === itemId ? { ...item, quantity: newQuantity } : item
      );
      const newTotal = updatedItems.reduce((sum, item) => sum + (item.price * item.quantity), 0);
      setCurrentOrder({ ...currentOrder, items: updatedItems, total: newTotal });
    }
  };

  // Remove order item
  const removeOrderItem = (orderId: string, itemId: string) => {
    setOrders(prevOrders =>
      prevOrders.map(order => {
        if (order.id === orderId) {
          const updatedItems = order.items.filter(item => item.id !== itemId);
          const newTotal = updatedItems.reduce((sum, item) => sum + (item.price * item.quantity), 0);
          return { ...order, items: updatedItems, total: newTotal };
        }
        return order;
      })
    );

    // Update current order if it's the same
    if (currentOrder?.id === orderId) {
      const updatedItems = currentOrder.items.filter(item => item.id !== itemId);
      const newTotal = updatedItems.reduce((sum, item) => sum + (item.price * item.quantity), 0);
      setCurrentOrder({ ...currentOrder, items: updatedItems, total: newTotal });
    }
  };

  // Add item to existing order
  const addItemToExistingOrder = (orderId: string, newItem: any) => {
    setOrders(prevOrders =>
      prevOrders.map(order => {
        if (order.id === orderId) {
          const existingItem = order.items.find(item => item.id === newItem.id);
          let updatedItems;

          if (existingItem) {
            updatedItems = order.items.map(item =>
              item.id === newItem.id
                ? { ...item, quantity: item.quantity + 1 }
                : item
            );
          } else {
            updatedItems = [...order.items, { ...newItem, quantity: 1 }];
          }

          const newTotal = updatedItems.reduce((sum, item) => sum + (item.price * item.quantity), 0);
          return { ...order, items: updatedItems, total: newTotal };
        }
        return order;
      })
    );

    // Update current order if it's the same
    if (currentOrder?.id === orderId) {
      const existingItem = currentOrder.items.find(item => item.id === newItem.id);
      let updatedItems;

      if (existingItem) {
        updatedItems = currentOrder.items.map(item =>
          item.id === newItem.id
            ? { ...item, quantity: item.quantity + 1 }
            : item
        );
      } else {
        updatedItems = [...currentOrder.items, { ...newItem, quantity: 1 }];
      }

      const newTotal = updatedItems.reduce((sum, item) => sum + (item.price * item.quantity), 0);
      setCurrentOrder({ ...currentOrder, items: updatedItems, total: newTotal });
    }
  };

  return (
    <div className="flex flex-col h-full" style={{ backgroundColor: '#f8f9fa' }}>
      {/* Enhanced Header */}
      <div
        className="relative overflow-hidden"
        style={{
          background: 'linear-gradient(135deg, #6c757d 0%, #495057 50%, #343a40 100%)',
          borderBottom: '3px solid #dee2e6'
        }}
      >
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute top-0 left-0 w-full h-full"
               style={{
                 backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
                 backgroundSize: '30px 30px'
               }}
          ></div>
        </div>

        <div className="relative p-8">
          <div className="flex justify-between items-center">
            <div className="flex items-center gap-6">
              {/* Enhanced Avatar */}
              <div className="relative">
                <div
                  className="w-16 h-16 rounded-full flex items-center justify-center shadow-lg border-4 border-white/20"
                  style={{
                    background: userType === "admin" 
                      ? 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
                      : 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
                  }}
                >
                  <span className="text-2xl">
                    {userType === "admin" ? "👨‍💼" : "👨‍💻"}
                  </span>
                </div>
                <div className="absolute -bottom-1 -right-1 w-6 h-6 rounded-full bg-green-400 border-2 border-white flex items-center justify-center">
                  <div className="w-2 h-2 rounded-full bg-white"></div>
                </div>
              </div>

              <div>
                <h1 className="text-3xl font-bold text-white mb-2 font-elegant">
                  {userType === "admin" ? "🏢 لوحة تحكم المدير" : "🍽️ نظام إدارة الطلبات"}
                </h1>
                <p className="text-white/80 text-lg font-elegant">
                  {userType === "admin" 
                    ? "مراقبة شاملة وتحليل الأداء" 
                    : "إدارة متقدمة للطلبات والطاولات"}
                </p>
                <div className="flex items-center gap-4 mt-2">
                  <div className="flex items-center gap-2 text-white/70">
                    <div className="w-2 h-2 rounded-full bg-green-400"></div>
                    <span className="text-sm font-elegant">متصل</span>
                  </div>
                  <div className="text-white/70 text-sm font-elegant">
                    آخر تحديث: {new Date().toLocaleTimeString('ar-SA')}
                  </div>
                </div>
              </div>
            </div>

            {/* Enhanced Status Cards */}
            <div className="flex items-center gap-4">
              <div
                className="px-6 py-4 rounded-xl shadow-lg backdrop-blur-sm border border-white/20"
                style={{
                  background: userType === "admin" 
                    ? 'rgba(255, 255, 255, 0.15)'
                    : 'rgba(255, 255, 255, 0.15)'
                }}
              >
                <div className="text-center">
                  <div className="text-2xl mb-1">
                    {userType === "admin" ? "📊" : "⚡"}
                  </div>
                  <div className="text-white font-bold text-lg font-elegant">
                    {userType === "admin" ? "مراقب" : "نشط"}
                  </div>
                  <div className="text-white/70 text-sm font-elegant">
                    {userType === "admin" ? "وضع العرض" : "وضع التشغيل"}
                  </div>
                </div>
              </div>

              <div
                className="px-6 py-4 rounded-xl shadow-lg backdrop-blur-sm border border-white/20"
                style={{ background: 'rgba(255, 255, 255, 0.15)' }}
              >
                <div className="text-center">
                  <div className="text-2xl mb-1">📅</div>
                  <div className="text-white font-bold text-lg font-elegant">
                    {new Date().toLocaleDateString('ar-SA', { 
                      weekday: 'short',
                      day: 'numeric',
                      month: 'short'
                    })}
                  </div>
                  <div className="text-white/70 text-sm font-elegant">
                    {new Date().toLocaleTimeString('ar-SA', {
                      hour: '2-digit',
                      minute: '2-digit'
                    })}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex flex-1 overflow-hidden">
        {/* Enhanced Left Panel - Order Type Selection (Cashier Only) */}
        {userType === "cashier" && (
          <div
            className="w-1/4 border-r shadow-lg"
            style={{ 
              background: 'linear-gradient(180deg, #ffffff 0%, #f8f9fa 100%)',
              borderColor: '#e9ecef'
            }}
          >
            {/* Section Header */}
            <div className="p-6 border-b" style={{ borderColor: '#e9ecef' }}>
              <div className="text-center mb-4">
                <div
                  className="w-12 h-12 mx-auto rounded-full flex items-center justify-center mb-3 shadow-md"
                  style={{
                    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                    color: 'white'
                  }}
                >
                  <span className="text-xl">🍽️</span>
                </div>
                <h3 className="text-xl font-bold font-elegant mb-2" style={{ color: '#343a40' }}>
                  أنواع الطلبات
                </h3>
                <p className="text-sm" style={{ color: '#6c757d' }}>
                  اختر نوع الطلب لبدء العمل
                </p>
              </div>
            </div>

            <div className="p-6">
              <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as any)}>
                {/* Enhanced Tab List */}
                <div className="grid grid-cols-1 gap-3 mb-6">
                  {[
                    { value: "dine-in", icon: "🍽️", title: "تناول في المطعم", desc: "طلبات الطاولات" },
                    { value: "delivery", icon: "🚚", title: "توصيل", desc: "طلبات التوصيل" },
                    { value: "takeaway", icon: "📦", title: "سفري", desc: "طلبات الاستلام" }
                  ].map((tab) => (
                    <button
                      key={tab.value}
                      onClick={() => setActiveTab(tab.value as any)}
                      className={`p-4 rounded-xl text-right transition-all duration-300 border-2 ${
                        activeTab === tab.value
                          ? 'shadow-lg transform scale-105'
                          : 'hover:shadow-md hover:transform hover:scale-102'
                      }`}
                      style={{
                        background: activeTab === tab.value
                          ? 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
                          : 'linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%)',
                        borderColor: activeTab === tab.value ? '#667eea' : '#e9ecef',
                        color: activeTab === tab.value ? 'white' : '#495057'
                      }}
                    >
                      <div className="flex items-center gap-3">
                        <div className={`text-2xl ${activeTab === tab.value ? 'animate-pulse' : ''}`}>
                          {tab.icon}
                        </div>
                        <div>
                          <div className="font-bold font-elegant text-lg">{tab.title}</div>
                          <div className={`text-sm font-elegant ${
                            activeTab === tab.value ? 'text-white/80' : 'text-gray-500'
                          }`}>
                            {tab.desc}
                          </div>
                        </div>
                      </div>
                    </button>
                  ))}
                </div>

                <TabsContent value="dine-in" className="mt-6 flex flex-col h-full">
                  {/* Enhanced Tables Section */}
                  <div className="mb-6 flex-shrink-0">
                    <div className="text-center mb-4">
                      <div
                        className="w-10 h-10 mx-auto rounded-full flex items-center justify-center mb-2 shadow-md"
                        style={{
                          background: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
                          color: 'white'
                        }}
                      >
                        <span className="text-lg">🪑</span>
                      </div>
                      <h4 className="text-lg font-bold font-elegant mb-1" style={{ color: '#343a40' }}>
                        اختر الطاولة
                      </h4>
                      <p className="text-sm" style={{ color: '#6c757d' }}>
                        الطاولات الخضراء متاحة، الحمراء مشغولة
                      </p>
                    </div>

                    {/* Tables Grid */}
                    <div className="grid grid-cols-2 gap-4">
                      {tables.map((table) => {
                        const isOccupied = orders.some(
                          (o) => o.type === "dine-in" && o.tableNumber === table && o.status !== "completed"
                        );

                        return (
                          <div key={table} className="space-y-2">
                            {/* Enhanced Table Card */}
                            <div
                              className={`relative p-4 rounded-xl border-2 cursor-pointer transition-all duration-300 ${
                                selectedTable === table
                                  ? 'shadow-xl transform scale-105 border-blue-400'
                                  : 'hover:shadow-lg hover:transform hover:scale-102'
                              }`}
                              style={{
                                background: selectedTable === table
                                  ? 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
                                  : isOccupied
                                  ? 'linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%)'
                                  : 'linear-gradient(135deg, #51cf66 0%, #40c057 100%)',
                                borderColor: selectedTable === table
                                  ? '#667eea'
                                  : isOccupied
                                  ? '#ff6b6b'
                                  : '#51cf66'
                              }}
                              onClick={() => handleTableSelect(table)}
                            >
                              {/* Status Indicator */}
                              <div className="absolute top-2 right-2 flex items-center gap-1">
                                <div className={`w-3 h-3 rounded-full ${
                                  isOccupied ? "bg-red-500" : "bg-green-500"
                                } shadow-md`}></div>
                                {isOccupied && (
                                  <div className="w-3 h-3 rounded-full bg-yellow-400 animate-pulse"></div>
                                )}
                              </div>

                              {/* Table Content */}
                              <div className="text-center">
                                <div className={`text-3xl mb-2 ${selectedTable === table ? 'animate-bounce' : ''}`}>
                                  🪑
                                </div>
                                <div className={`font-bold text-lg font-elegant mb-1 ${
                                  selectedTable === table ? 'text-white' : 'text-gray-700'
                                }`}>
                                  طاولة {table}
                                </div>
                                <div className={`text-sm font-elegant mb-2 ${
                                  selectedTable === table ? 'text-white/80' : 'text-gray-600'
                                }`}>
                                  {isOccupied ? "🔴 مشغولة" : "🟢 متاحة"}
                                </div>

                                {/* Timer Display */}
                                {isOccupied && getTableDuration(table) && (
                                  <div className={`text-xs font-mono px-2 py-1 rounded-full ${
                                    selectedTable === table
                                      ? 'bg-white/20 text-white'
                                      : 'bg-gray-700/20 text-gray-700'
                                  }`}>
                                    ⏱️ {getTableDuration(table)}
                                  </div>
                                )}
                              </div>
                            </div>

                            {/* Enhanced Action Buttons */}
                            {isOccupied && (
                              <div className="space-y-2 mt-3">
                                <div className="grid grid-cols-2 gap-2">
                                  <button
                                    className="px-3 py-2 rounded-lg text-xs font-bold font-elegant transition-all duration-200 shadow-md hover:shadow-lg transform hover:scale-105"
                                    style={{
                                      background: 'linear-gradient(135deg, #ff9a56 0%, #ff6b6b 100%)',
                                      color: 'white',
                                      border: 'none'
                                    }}
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      const tableOrder = orders.find(
                                        (o) => o.type === "dine-in" && o.tableNumber === table && o.status !== "completed"
                                      );
                                      if (tableOrder) {
                                        prepareOrder(tableOrder);
                                      }
                                    }}
                                  >
                                    🍳 تجهيز
                                  </button>
                                  <button
                                    className="px-3 py-2 rounded-lg text-xs font-bold font-elegant transition-all duration-200 shadow-md hover:shadow-lg transform hover:scale-105"
                                    style={{
                                      background: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
                                      color: 'white',
                                      border: 'none'
                                    }}
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      const tableOrder = orders.find(
                                        (o) => o.type === "dine-in" && o.tableNumber === table && o.status !== "completed"
                                      );
                                      if (tableOrder) {
                                        const completedOrder = { ...tableOrder, status: "completed" as const };
                                        saveToDatabase(completedOrder);
                                        setOrders((prevOrders) =>
                                          prevOrders.map((order) =>
                                            order.id === tableOrder.id ? { ...order, status: "completed" as const } : order
                                          )
                                        );
                                        if (currentOrder?.tableNumber === table) {
                                          setCurrentOrder(null);
                                        }
                                        if (selectedTable === table) {
                                          setSelectedTable(null);
                                        }
                                        setTableStartTimes(prev => {
                                          const newTimes = { ...prev };
                                          delete newTimes[table];
                                          return newTimes;
                                        });
                                      }
                                    }}
                                  >
                                    💳 دفع
                                  </button>
                                </div>
                                <button
                                  className="w-full px-3 py-2 rounded-lg text-xs font-bold font-elegant transition-all duration-200 shadow-md hover:shadow-lg transform hover:scale-105"
                                  style={{
                                    background: 'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)',
                                    color: '#495057',
                                    border: 'none'
                                  }}
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    setEditingTableNumber(table);
                                    setIsEditTableOpen(true);
                                  }}
                                >
                                  ✏️ تعديل الطاولة
                                </button>
                              </div>
                            )}
                          </div>
                        );
                      })}
                    </div>
                  </div>

                  {/* Open Tables Section */}
                  {getOccupiedTables().length > 0 && (
                    <div className="mt-8">
                      <div className="flex items-center justify-between mb-4">
                        <h4 className="text-lg font-bold font-elegant" style={{ color: '#343a40' }}>
                          🔥 الطاولات المفتوحة ({getOccupiedTables().length})
                        </h4>
                        <button
                          onClick={() => setShowOpenTables(!showOpenTables)}
                          className="px-3 py-1 rounded-lg text-sm font-bold transition-all duration-200"
                          style={{
                            background: showOpenTables
                              ? 'linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%)'
                              : 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                            color: 'white',
                            border: 'none'
                          }}
                        >
                          {showOpenTables ? '🔼 إخفاء' : '🔽 عرض'}
                        </button>
                      </div>

                      {showOpenTables && (
                        <div className="space-y-3 max-h-64 overflow-y-auto">
                          {getOccupiedTables().map((order) => (
                            <div
                              key={order.id}
                              className="p-4 rounded-xl border-2 transition-all duration-300 hover:shadow-lg"
                              style={{
                                background: 'linear-gradient(135deg, #fff5f5 0%, #fed7d7 100%)',
                                borderColor: '#ff6b6b'
                              }}
                            >
                              <div className="flex items-center justify-between mb-3">
                                <div className="flex items-center gap-3">
                                  <div
                                    className="w-8 h-8 rounded-full flex items-center justify-center text-white font-bold"
                                    style={{ background: 'linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%)' }}
                                  >
                                    {order.tableNumber}
                                  </div>
                                  <div>
                                    <div className="font-bold font-elegant" style={{ color: '#343a40' }}>
                                      {order.customerName || 'عميل'}
                                    </div>
                                    <div className="text-sm" style={{ color: '#6c757d' }}>
                                      {order.items.length} منتج - {order.total.toFixed(2)} ر.س
                                    </div>
                                  </div>
                                </div>
                                <div className="flex items-center gap-2">
                                  {getTableDuration(order.tableNumber!) && (
                                    <div className="text-xs font-mono px-2 py-1 rounded-full bg-orange-100 text-orange-700">
                                      ⏱️ {getTableDuration(order.tableNumber!)}
                                    </div>
                                  )}
                                </div>
                              </div>

                              {/* Order Items */}
                              <div className="space-y-2 mb-4">
                                {order.items.map((item, index) => (
                                  <div key={index} className="flex items-center justify-between p-2 bg-white rounded-lg">
                                    <div className="flex-1">
                                      <span className="font-medium">{item.name}</span>
                                      <span className="text-sm text-gray-500 ml-2">({item.price} ر.س)</span>
                                    </div>
                                    <div className="flex items-center gap-2">
                                      <button
                                        onClick={() => updateOrderItemQuantity(order.id, item.id, item.quantity - 1)}
                                        className="w-6 h-6 rounded-full bg-red-100 text-red-600 hover:bg-red-200 flex items-center justify-center text-sm font-bold"
                                      >
                                        -
                                      </button>
                                      <span className="w-8 text-center font-bold">{item.quantity}</span>
                                      <button
                                        onClick={() => updateOrderItemQuantity(order.id, item.id, item.quantity + 1)}
                                        className="w-6 h-6 rounded-full bg-green-100 text-green-600 hover:bg-green-200 flex items-center justify-center text-sm font-bold"
                                      >
                                        +
                                      </button>
                                      <button
                                        onClick={() => removeOrderItem(order.id, item.id)}
                                        className="w-6 h-6 rounded-full bg-red-500 text-white hover:bg-red-600 flex items-center justify-center text-sm font-bold ml-2"
                                      >
                                        ×
                                      </button>
                                      <span className="font-bold text-green-600 ml-2">
                                        {(item.price * item.quantity).toFixed(2)} ر.س
                                      </span>
                                    </div>
                                  </div>
                                ))}
                              </div>

                              {/* Action Buttons */}
                              <div className="flex gap-2">
                                <button
                                  onClick={() => {
                                    setCurrentOrder(order);
                                    setSelectedTable(order.tableNumber!);
                                  }}
                                  className="flex-1 px-3 py-2 rounded-lg text-sm font-bold transition-all duration-200"
                                  style={{
                                    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                                    color: 'white',
                                    border: 'none'
                                  }}
                                >
                                  ✏️ تعديل الطلب
                                </button>
                                <button
                                  onClick={() => prepareOrder(order)}
                                  className="flex-1 px-3 py-2 rounded-lg text-sm font-bold transition-all duration-200"
                                  style={{
                                    background: 'linear-gradient(135deg, #ff9a56 0%, #ff6b6b 100%)',
                                    color: 'white',
                                    border: 'none'
                                  }}
                                >
                                  🍳 تجهيز
                                </button>
                                <button
                                  onClick={() => completeTablePayment(order.tableNumber!)}
                                  className="flex-1 px-3 py-2 rounded-lg text-sm font-bold transition-all duration-200"
                                  style={{
                                    background: 'linear-gradient(135deg, #51cf66 0%, #40c057 100%)',
                                    color: 'white',
                                    border: 'none'
                                  }}
                                >
                                  💳 تم الدفع
                                </button>
                              </div>
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  )}
                </TabsContent>

                <TabsContent value="delivery" className="mt-6">
                  <div className="text-center py-8">
                    <div className="text-4xl mb-4">🚚</div>
                    <h4 className="text-lg font-bold font-elegant mb-2" style={{ color: '#343a40' }}>
                      طلبات التوصيل
                    </h4>
                    <p className="text-sm" style={{ color: '#6c757d' }}>
                      إدارة طلبات التوصيل للعملاء
                    </p>
                  </div>
                </TabsContent>

                <TabsContent value="takeaway" className="mt-6">
                  <div className="text-center py-8">
                    <div className="text-4xl mb-4">📦</div>
                    <h4 className="text-lg font-bold font-elegant mb-2" style={{ color: '#343a40' }}>
                      طلبات سفري
                    </h4>
                    <p className="text-sm" style={{ color: '#6c757d' }}>
                      إدارة طلبات الاستلام من المطعم
                    </p>
                  </div>
                </TabsContent>
              </Tabs>
            </div>
          </div>
        )}

        {/* Middle Panel - Order Details or Admin Dashboard */}
        <div className={userType === "cashier" ? "w-5/12" : "w-1/2"} style={{ backgroundColor: '#f8f9fa' }}>
          {userType === "admin" ? (
            <div className="p-8 h-full" style={{ backgroundColor: '#f8f9fa' }}>
              {/* Enhanced Admin Dashboard */}
              <div className="max-w-md mx-auto">
                {/* Welcome Section */}
                <div className="text-center mb-8">
                  <div className="relative mb-6">
                    <div
                      className="w-24 h-24 mx-auto rounded-full flex items-center justify-center shadow-xl border-4 border-white"
                      style={{
                        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                        color: 'white'
                      }}
                    >
                      <span className="text-3xl">👨‍💼</span>
                    </div>
                    <div className="absolute -bottom-2 -right-2 w-8 h-8 rounded-full bg-green-400 border-4 border-white flex items-center justify-center shadow-lg">
                      <div className="w-3 h-3 rounded-full bg-white animate-pulse"></div>
                    </div>
                  </div>
                  <h3 className="text-2xl font-bold font-elegant mb-2" style={{ color: '#343a40' }}>
                    🏢 لوحة تحكم المدير
                  </h3>
                  <p className="text-lg font-elegant" style={{ color: '#6c757d' }}>
                    إدارة شاملة ومراقبة متقدمة
                  </p>
                </div>

                {/* Enhanced Feature Cards */}
                <div className="space-y-4 mb-8">
                  {[
                    {
                      icon: "📊",
                      title: "تقارير المبيعات",
                      desc: "تحليل مفصل مع بحث بالتاريخ والوقت",
                      gradient: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
                      action: "عرض التقارير"
                    },
                    {
                      icon: "🍽️",
                      title: "إدارة المخزون",
                      desc: "تعديل وإضافة المنتجات والأسعار",
                      gradient: "linear-gradient(135deg, #f093fb 0%, #f5576c 100%)",
                      action: "إدارة المنتجات"
                    },
                    {
                      icon: "👁️",
                      title: "مراقبة الطلبات",
                      desc: "عرض مباشر للطلبات النشطة",
                      gradient: "linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)",
                      action: "مراقبة مباشرة"
                    }
                  ].map((feature, index) => (
                    <div
                      key={index}
                      className="p-6 rounded-xl shadow-lg border-2 border-gray-200 hover:shadow-xl transition-all duration-300 cursor-pointer transform hover:scale-105"
                      style={{
                        background: 'linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%)',
                      }}
                    >
                      <div className="flex items-center gap-4">
                        <div
                          className="w-12 h-12 rounded-full flex items-center justify-center shadow-md"
                          style={{ background: feature.gradient, color: 'white' }}
                        >
                          <span className="text-xl">{feature.icon}</span>
                        </div>
                        <div className="flex-1">
                          <h4 className="font-bold font-elegant text-lg mb-1" style={{ color: '#343a40' }}>
                            {feature.title}
                          </h4>
                          <p className="text-sm font-elegant" style={{ color: '#6c757d' }}>
                            {feature.desc}
                          </p>
                        </div>
                        <div
                          className="px-4 py-2 rounded-lg text-sm font-bold font-elegant text-white shadow-md"
                          style={{ background: feature.gradient }}
                        >
                          {feature.action}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>

                {/* Enhanced Tip Section */}
                <div
                  className="p-6 rounded-xl shadow-lg border-2"
                  style={{
                    background: 'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)',
                    borderColor: '#4facfe'
                  }}
                >
                  <div className="flex items-center gap-3">
                    <div className="text-2xl">💡</div>
                    <div>
                      <div className="font-bold font-elegant text-lg" style={{ color: '#343a40' }}>
                        نصيحة سريعة
                      </div>
                      <div className="text-sm font-elegant" style={{ color: '#495057' }}>
                        استخدم تبويب "إدارة المخزون" لتعديل المنتجات والأسعار
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ) : (
            <div className="p-6 h-full">
              {currentOrder ? (
                <div className="h-full flex flex-col">
                  <div className="mb-6">
                    <h3 className="text-xl font-bold font-elegant mb-2" style={{ color: '#343a40' }}>
                      📋 تفاصيل الطلب
                    </h3>
                    <p className="text-sm" style={{ color: '#6c757d' }}>
                      {activeTab === 'dine-in' ? `طاولة ${selectedTable}` :
                       activeTab === 'delivery' ? 'طلب توصيل' : 'طلب سفري'}
                    </p>
                  </div>

                  <div className="space-y-4 mb-6">
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="customerName">اسم العميل *</Label>
                        <Input
                          id="customerName"
                          value={currentOrder.customerName}
                          onChange={(e) => setCurrentOrder(prev =>
                            prev ? { ...prev, customerName: e.target.value } : null
                          )}
                          placeholder="أدخل اسم العميل"
                        />
                      </div>
                      <div>
                        <Label htmlFor="customerPhone">رقم الهاتف</Label>
                        <Input
                          id="customerPhone"
                          value={currentOrder.customerPhone}
                          onChange={(e) => setCurrentOrder(prev =>
                            prev ? { ...prev, customerPhone: e.target.value } : null
                          )}
                          placeholder="أدخل رقم الهاتف"
                        />
                      </div>
                    </div>

                    {activeTab === 'delivery' && (
                      <div>
                        <Label htmlFor="customerAddress">عنوان التوصيل</Label>
                        <Textarea
                          id="customerAddress"
                          value={currentOrder.customerAddress || ''}
                          onChange={(e) => setCurrentOrder(prev =>
                            prev ? { ...prev, customerAddress: e.target.value } : null
                          )}
                          placeholder="أدخل عنوان التوصيل"
                        />
                      </div>
                    )}
                  </div>

                  <div className="flex-1 overflow-hidden">
                    <h4 className="text-lg font-bold font-elegant mb-4" style={{ color: '#343a40' }}>
                      🛒 عناصر الطلب ({currentOrder.items.length})
                    </h4>

                    {currentOrder.items.length > 0 ? (
                      <div className="space-y-2 max-h-64 overflow-y-auto mb-4">
                        {currentOrder.items.map((item, index) => (
                          <div key={index} className="flex justify-between items-center p-3 bg-white rounded-lg shadow-sm border">
                            <div>
                              <div className="font-medium">{item.name}</div>
                              <div className="text-sm text-gray-500">{item.price} ر.س × {item.quantity}</div>
                            </div>
                            <div className="text-lg font-bold" style={{ color: '#28a745' }}>
                              {(item.price * item.quantity).toFixed(2)} ر.س
                            </div>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <div className="text-center py-8 text-gray-500">
                        لم يتم إضافة أي عناصر بعد
                      </div>
                    )}

                    {currentOrder.items.length > 0 && (
                      <div className="border-t pt-4 space-y-2">
                        <div className="flex justify-between text-lg">
                          <span>المجموع الفرعي:</span>
                          <span>{currentOrder.total.toFixed(2)} ر.س</span>
                        </div>
                        <div className="flex justify-between text-lg">
                          <span>الضريبة (15%):</span>
                          <span>{(currentOrder.total * 0.15).toFixed(2)} ر.س</span>
                        </div>
                        <div className="flex justify-between text-xl font-bold" style={{ color: '#28a745' }}>
                          <span>المجموع الكلي:</span>
                          <span>{(currentOrder.total * 1.15).toFixed(2)} ر.س</span>
                        </div>
                      </div>
                    )}
                  </div>

                  <div className="mt-6">
                    <Button
                      onClick={completeOrder}
                      className="w-full"
                      disabled={!currentOrder.customerName.trim() || currentOrder.items.length === 0}
                    >
                      💳 إتمام الطلب والدفع
                    </Button>
                  </div>
                </div>
              ) : (
                <div className="h-full flex items-center justify-center">
                  <div className="text-center">
                    <div className="text-6xl mb-4">🍽️</div>
                    <h3 className="text-xl font-bold font-elegant mb-2" style={{ color: '#343a40' }}>
                      اختر طاولة أو ابدأ طلب جديد
                    </h3>
                    <p className="text-sm" style={{ color: '#6c757d' }}>
                      اختر طاولة من اليسار أو ابدأ طلب توصيل/سفري
                    </p>
                  </div>
                </div>
              )}
            </div>
          )}
        </div>

        {/* Enhanced Right Panel - Product Catalog */}
        <div className={userType === "cashier" ? "w-5/12" : "w-1/2"} style={{ display: 'flex', flexDirection: 'column', height: '100%' }}>
          {userType === "admin" ? (
            <div className="flex flex-col h-full" style={{ backgroundColor: '#f8f9fa' }}>
              {/* Enhanced Admin Products Header */}
              <div className="p-6 flex-shrink-0 border-b" style={{ borderColor: '#e9ecef' }}>
                <div className="text-center">
                  <div
                    className="w-12 h-12 mx-auto rounded-full flex items-center justify-center mb-3 shadow-md"
                    style={{
                      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                      color: 'white'
                    }}
                  >
                    <span className="text-xl">📋</span>
                  </div>
                  <h3 className="text-xl font-bold font-elegant mb-2" style={{ color: '#343a40' }}>
                    عرض المنتجات
                  </h3>
                  <p className="text-sm font-elegant" style={{ color: '#6c757d' }}>
                    للتعديل، استخدم تبويب "إدارة المخزون"
                  </p>
                </div>
              </div>
              <div className="flex-1 overflow-hidden">
                <ProductCatalog
                  showInventoryControls={false}
                  onAddToOrder={() => {}}
                  language={language}
                />
              </div>
            </div>
          ) : (
            <div className="flex flex-col h-full" style={{ backgroundColor: '#f8f9fa' }}>
              {/* Enhanced Cashier Products Header */}
              <div className="p-6 flex-shrink-0 border-b" style={{ borderColor: '#e9ecef' }}>
                <div className="text-center">
                  <div
                    className="w-12 h-12 mx-auto rounded-full flex items-center justify-center mb-3 shadow-md"
                    style={{
                      background: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
                      color: 'white'
                    }}
                  >
                    <span className="text-xl">🛒</span>
                  </div>
                  <h3 className="text-xl font-bold font-elegant mb-2" style={{ color: '#343a40' }}>
                    كتالوج المنتجات
                  </h3>
                  <p className="text-sm font-elegant" style={{ color: '#6c757d' }}>
                    اختر المنتجات لإضافتها للطلب
                  </p>

                  {/* Quick Stats */}
                  <div className="flex justify-center gap-4 mt-4">
                    <div
                      className="px-3 py-2 rounded-lg shadow-sm"
                      style={{ backgroundColor: '#e3f2fd', color: '#1976d2' }}
                    >
                      <div className="text-xs font-elegant">المنتجات</div>
                      <div className="font-bold">16</div>
                    </div>
                    <div
                      className="px-3 py-2 rounded-lg shadow-sm"
                      style={{ backgroundColor: '#e8f5e8', color: '#388e3c' }}
                    >
                      <div className="text-xs font-elegant">الفئات</div>
                      <div className="font-bold">4</div>
                    </div>
                  </div>
                </div>
              </div>
              <div className="flex-1 overflow-hidden">
                <ProductCatalog
                  showInventoryControls={false}
                  onAddToOrder={(item) => {
                    if (currentOrder) {
                      // Add to existing order
                      addItemToExistingOrder(currentOrder.id, item);
                    } else {
                      // Create new order
                      addItemToOrder(item);
                    }
                  }}
                  language={language}
                />
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Edit Table Dialog */}
      <Dialog open={isEditTableOpen} onOpenChange={setIsEditTableOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>تعديل طاولة {editingTableNumber}</DialogTitle>
          </DialogHeader>
          <div className="text-center py-8">
            <div className="text-4xl mb-4">🔧</div>
            <p>سيتم إضافة خيارات تعديل الطاولة قريباً</p>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default OrderManagement;
