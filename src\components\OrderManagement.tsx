import React, { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent } from "@/components/ui/tabs";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  ChevronRight,
  Plus,
  Trash2,
  CreditCard,
  Receipt,
  Clock,
  CheckCircle,
  AlertCircle,
  Printer,
  Settings,
} from "lucide-react";
import ProductCatalog from "./ProductCatalog";
import OrderDetails from "./OrderDetails";

interface OrderItem {
  id: string;
  name: string;
  price: number;
  quantity: number;
}

interface Order {
  id: string;
  type: "dine-in" | "delivery" | "takeaway";
  tableNumber?: number;
  customerName?: string;
  customerPhone?: string;
  customerAddress?: string;
  items: OrderItem[];
  status: "new" | "in-progress" | "completed";
  total: number;
  createdAt: Date;
}

interface OrderManagementProps {
  language?: "english" | "arabic";
  userType?: "admin" | "cashier";
}

const OrderManagement = ({
  language = "arabic",
  userType = "cashier",
}: OrderManagementProps) => {
  const [activeTab, setActiveTab] = useState<"dine-in" | "delivery" | "takeaway">("dine-in");
  const [selectedTable, setSelectedTable] = useState<number | null>(null);
  const [customerInfo, setCustomerInfo] = useState({
    name: "",
    phone: "",
    address: "",
  });
  const [currentOrder, setCurrentOrder] = useState<Order | null>(null);
  const [tableCount, setTableCount] = useState(10);
  const [tableStartTimes, setTableStartTimes] = useState<{[key: number]: Date}>({});
  const [isEditTableOpen, setIsEditTableOpen] = useState(false);
  const [editingTableNumber, setEditingTableNumber] = useState<number | null>(null);
  const [orders, setOrders] = useState<Order[]>([
    {
      id: "1",
      type: "dine-in",
      tableNumber: 1,
      items: [
        { id: "101", name: "Burger", price: 8.99, quantity: 2 },
        { id: "102", name: "Fries", price: 3.99, quantity: 1 },
      ],
      status: "in-progress",
      total: 21.97,
      createdAt: new Date(),
    },
    {
      id: "2",
      type: "delivery",
      customerName: "John Doe",
      customerPhone: "555-1234",
      customerAddress: "123 Main St",
      items: [{ id: "103", name: "Pizza", price: 12.99, quantity: 1 }],
      status: "new",
      total: 12.99,
      createdAt: new Date(),
    },
  ]);

  const tables = Array.from({ length: tableCount }, (_, i) => i + 1);
  const [selectedCategory, setSelectedCategory] = useState("جميع الفئات");
  const [newOrder, setNewOrder] = useState({
    customerName: "",
    customerPhone: "",
    orderType: "dine-in" as "dine-in" | "delivery" | "takeaway",
    paymentMethod: "cash" as "cash" | "card" | "online",
    tableNumber: "",
    deliveryAddress: "",
    notes: "",
    items: [] as any[],
  });

  // Complete menu items - 16 products
  const sampleMenuItems = [
    // أطباق رئيسية
    { id: "1", name: "كبسة دجاج", price: 45, category: "أطباق رئيسية", description: "أرز بسمتي مع دجاج مشوي وخلطة البهارات الخاصة" },
    { id: "2", name: "مندي لحم", price: 65, category: "أطباق رئيسية", description: "لحم خروف طري مع أرز مندي مدخن" },
    { id: "3", name: "مشاوي مشكلة", price: 85, category: "أطباق رئيسية", description: "تشكيلة من اللحوم المشوية مع الخضار" },
    { id: "4", name: "فراخ مشوية", price: 38, category: "أطباق رئيسية", description: "دجاج كامل مشوي مع البهارات والأعشاب" },

    // مقبلات
    { id: "5", name: "حمص بالطحينة", price: 18, category: "مقبلات", description: "حمص كريمي مع الطحينة وزيت الزيتون" },
    { id: "6", name: "فتوش", price: 22, category: "مقبلات", description: "سلطة خضار مشكلة مع الخبز المحمص" },
    { id: "7", name: "تبولة", price: 20, category: "مقبلات", description: "سلطة البقدونس مع الطماطم والبرغل" },
    { id: "8", name: "بابا غنوج", price: 16, category: "مقبلات", description: "باذنجان مشوي مع الطحينة والثوم" },

    // مشروبات
    { id: "9", name: "عصير برتقال طازج", price: 12, category: "مشروبات", description: "عصير برتقال طبيعي 100%" },
    { id: "10", name: "شاي أحمر", price: 8, category: "مشروبات", description: "شاي أحمر تقليدي مع السكر" },
    { id: "11", name: "قهوة عربية", price: 10, category: "مشروبات", description: "قهوة عربية أصيلة مع الهيل" },
    { id: "12", name: "عصير مانجو", price: 15, category: "مشروبات", description: "عصير مانجو طازج ومنعش" },

    // حلويات
    { id: "13", name: "كنافة نابلسية", price: 25, category: "حلويات", description: "كنافة محشوة بالجبن مع القطر" },
    { id: "14", name: "مهلبية", price: 15, category: "حلويات", description: "حلى كريمي بالحليب مع ماء الورد" },
    { id: "15", name: "بقلاوة", price: 20, category: "حلويات", description: "حلوى شرقية بالعجين الرقيق والمكسرات" },
    { id: "16", name: "أم علي", price: 18, category: "حلويات", description: "حلى ساخن بالحليب والمكسرات والزبيب" }
  ];

  // Database simulation
  const saveToDatabase = (order: Order) => {
    try {
      const existingOrders = JSON.parse(localStorage.getItem("restaurant_orders") || "[]");
      const updatedOrders = [...existingOrders, { ...order, id: `order-${Date.now()}` }];
      localStorage.setItem("restaurant_orders", JSON.stringify(updatedOrders));
      console.log("Order saved to database:", order);
    } catch (error) {
      console.error("Error saving to database:", error);
    }
  };

  const loadFromDatabase = () => {
    try {
      const savedOrders = JSON.parse(localStorage.getItem("restaurant_orders") || "[]");
      setOrders(savedOrders);
    } catch (error) {
      console.error("Error loading from database:", error);
    }
  };

  useEffect(() => {
    loadFromDatabase();
  }, []);

  const handleTableSelect = (tableNumber: number) => {
    setSelectedTable(tableNumber);
    const existingOrder = orders.find(
      (order) => order.type === "dine-in" && order.tableNumber === tableNumber && order.status !== "completed"
    );

    if (existingOrder) {
      setCurrentOrder(existingOrder);
    } else {
      const newOrder: Order = {
        id: `order-${Date.now()}`,
        type: "dine-in",
        tableNumber: tableNumber,
        items: [],
        status: "new",
        total: 0,
        createdAt: new Date(),
      };
      setCurrentOrder(newOrder);
      setTableStartTimes(prev => ({ ...prev, [tableNumber]: new Date() }));
    }
  };

  const getTableDuration = (tableNumber: number) => {
    const startTime = tableStartTimes[tableNumber];
    if (!startTime) return null;
    const now = new Date();
    const diffMs = now.getTime() - startTime.getTime();
    const diffMins = Math.floor(diffMs / 60000);
    const hours = Math.floor(diffMins / 60);
    const minutes = diffMins % 60;
    if (hours > 0) {
      return `${hours}س ${minutes}د`;
    }
    return `${minutes}د`;
  };

  const handleCustomerInfoChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setCustomerInfo((prev) => ({ ...prev, [name]: value }));
  };

  const createDeliveryOrder = () => {
    if (!customerInfo.name || !customerInfo.phone || !customerInfo.address) {
      return;
    }
    const newOrder: Order = {
      id: `order-${Date.now()}`,
      type: "delivery",
      customerName: customerInfo.name,
      customerPhone: customerInfo.phone,
      customerAddress: customerInfo.address,
      items: [],
      status: "new",
      total: 0,
      createdAt: new Date(),
    };
    setCurrentOrder(newOrder);
  };

  const createTakeawayOrder = () => {
    const newOrder: Order = {
      id: `order-${Date.now()}`,
      type: "takeaway",
      items: [],
      status: "new",
      total: 0,
      createdAt: new Date(),
    };
    setCurrentOrder(newOrder);
  };

  const addItemToOrder = (item: { id: string; name: string; price: number; }) => {
    if (!currentOrder) return;
    setCurrentOrder((prevOrder) => {
      if (!prevOrder) return null;
      const existingItem = prevOrder.items.find((i) => i.id === item.id);
      let updatedItems;
      if (existingItem) {
        updatedItems = prevOrder.items.map((i) =>
          i.id === item.id ? { ...i, quantity: i.quantity + 1 } : i
        );
      } else {
        updatedItems = [...prevOrder.items, { ...item, quantity: 1 }];
      }
      const total = updatedItems.reduce((sum, item) => sum + item.price * item.quantity, 0);
      return { ...prevOrder, items: updatedItems, total };
    });
  };

  const saveOrder = () => {
    if (!currentOrder || currentOrder.items.length === 0) return;
    const completedOrder = { ...currentOrder, status: "completed" as const };
    saveToDatabase(completedOrder);
    setOrders((prevOrders) => {
      const orderExists = prevOrders.some((order) => order.id === currentOrder.id);
      if (orderExists) {
        return prevOrders.map((order) => order.id === currentOrder.id ? completedOrder : order);
      } else {
        return [...prevOrders, completedOrder];
      }
    });
    setSelectedTable(null);
    setCustomerInfo({ name: "", phone: "", address: "" });
    setCurrentOrder(null);
  };

  const updateOrderStatus = (orderId: string, status: string) => {
    setOrders((prevOrders) =>
      prevOrders.map((order) => order.id === orderId ? { ...order, status: status as any } : order)
    );
  };

  const printOrder = (order: Order) => {
    console.log("Printing order:", order);
    alert("تم إرسال الطلب للطباعة");
  };

  const prepareOrder = (order: Order) => {
    console.log("Preparing order for kitchen:", order);
    alert("تم إرسال الطلب للمطبخ للتجهيز");
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "new":
        return <Badge variant="outline" className="bg-blue-100 text-blue-800">New</Badge>;
      case "in-progress":
        return <Badge variant="outline" className="bg-yellow-100 text-yellow-800">In Progress</Badge>;
      case "completed":
        return <Badge variant="outline" className="bg-green-100 text-green-800">Completed</Badge>;
      default:
        return null;
    }
  };

  return (
    <div className="flex flex-col h-full bg-background">
      <div
        className="p-6 border-b flex justify-between items-center"
        style={{ backgroundColor: '#e9ecef', borderColor: '#dee2e6' }}
      >
        <div className="flex items-center gap-4">
          <div
            className="w-12 h-12 rounded-full flex items-center justify-center"
            style={{ backgroundColor: '#6c757d', color: 'white' }}
          >
            <span className="text-xl">
              {userType === "admin" ? "👨‍💼" : "👨‍💻"}
            </span>
          </div>
          <div>
            <h2 className="text-2xl font-bold font-elegant" style={{ color: '#343a40' }}>
              إدارة الطلبات
            </h2>
            <p className="text-sm" style={{ color: '#6c757d' }}>
              {userType === "admin" ? "وضع المدير - عرض ومراقبة" : "وضع الكاشير - إدارة كاملة"}
            </p>
          </div>
        </div>

        <div className="flex items-center gap-3">
          <div
            className="px-4 py-2 rounded-lg"
            style={{
              backgroundColor: userType === "admin" ? '#e3f2fd' : '#e8f5e8',
              color: userType === "admin" ? '#1976d2' : '#388e3c'
            }}
          >
            <span className="text-sm font-medium font-elegant">
              {userType === "admin" ? "🔍 مراقب" : "⚡ نشط"}
            </span>
          </div>

          <div className="text-right">
            <div className="text-sm font-medium" style={{ color: '#495057' }}>
              {new Date().toLocaleDateString('ar-SA')}
            </div>
            <div className="text-xs" style={{ color: '#6c757d' }}>
              {new Date().toLocaleTimeString('ar-SA')}
            </div>
          </div>
        </div>
      </div>

      <div className="flex flex-1 overflow-hidden">
        {/* Left Panel - Order Type Selection (Cashier Only) */}
        {userType === "cashier" && (
          <div
            className="w-1/4 border-r p-6"
            style={{ backgroundColor: '#f8f9fa', borderColor: '#dee2e6' }}
          >
            <div className="mb-6">
              <h3 className="text-lg font-bold font-elegant mb-2" style={{ color: '#495057' }}>
                🍽️ أنواع الطلبات
              </h3>
              <p className="text-sm mb-4" style={{ color: '#6c757d' }}>
                اختر نوع الطلب لبدء العمل
              </p>
            </div>

            <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as any)}>
              <TabsList className="grid grid-cols-3 w-full p-1" style={{ backgroundColor: '#e9ecef' }}>
                <TabsTrigger value="dine-in" className="font-elegant text-sm" style={{ color: '#495057' }}>
                  🍽️ تناول في المطعم
                </TabsTrigger>
                <TabsTrigger value="delivery" className="font-elegant text-sm" style={{ color: '#495057' }}>
                  🚚 توصيل
                </TabsTrigger>
                <TabsTrigger value="takeaway" className="font-elegant text-sm" style={{ color: '#495057' }}>
                  📦 سفري
                </TabsTrigger>
              </TabsList>

              <TabsContent value="dine-in" className="mt-6 flex flex-col h-full">
                <div className="mb-4 flex-shrink-0">
                  <h4 className="text-md font-bold font-elegant mb-2" style={{ color: '#495057' }}>
                    🪑 اختر الطاولة
                  </h4>
                  <p className="text-xs" style={{ color: '#6c757d' }}>
                    الطاولات الخضراء متاحة، الحمراء مشغولة
                  </p>
                </div>

                <div className="flex-shrink-0 mb-4">
                  <div className="grid grid-cols-2 gap-3">
                    {tables.map((table) => {
                      const isOccupied = orders.some(
                        (o) => o.type === "dine-in" && o.tableNumber === table && o.status !== "completed"
                      );

                      return (
                        <div key={table} className="space-y-1">
                          <Button
                            variant={selectedTable === table ? "default" : "outline"}
                            className={`h-24 w-full relative font-elegant transition-all duration-200 ${
                              selectedTable === table
                                ? "bg-blue-500 text-white border-blue-500 shadow-lg"
                                : isOccupied
                                ? "bg-red-50 border-red-300 hover:bg-red-100 text-red-700"
                                : "bg-green-50 border-green-300 hover:bg-green-100 text-green-700"
                            }`}
                            onClick={() => handleTableSelect(table)}
                          >
                            <div className="text-center w-full">
                              <div className="text-lg font-bold">🪑</div>
                              <div className="text-sm font-medium">طاولة {table}</div>
                              <div className="text-xs">{isOccupied ? "مشغولة" : "متاحة"}</div>
                              {isOccupied && getTableDuration(table) && (
                                <div className="text-xs font-mono mt-1 px-1 py-0.5 rounded"
                                     style={{ backgroundColor: 'rgba(255,255,255,0.2)' }}>
                                  ⏱️ {getTableDuration(table)}
                                </div>
                              )}
                            </div>
                            <div className={`absolute top-2 right-2 w-3 h-3 rounded-full ${
                              isOccupied ? "bg-red-500" : "bg-green-500"
                            }`}></div>
                          </Button>
                          {isOccupied && (
                            <div className="space-y-1 mt-2">
                              <div className="flex gap-1">
                                <Button
                                  size="sm"
                                  variant="outline"
                                  className="flex-1 text-xs font-elegant bg-orange-50 hover:bg-orange-100 border-orange-300 text-orange-700"
                                  onClick={() => {
                                    const tableOrder = orders.find(
                                      (o) => o.type === "dine-in" && o.tableNumber === table && o.status !== "completed"
                                    );
                                    if (tableOrder) {
                                      prepareOrder(tableOrder);
                                    }
                                  }}
                                >
                                  🍳 تجهيز
                                </Button>
                                <Button
                                  size="sm"
                                  variant="outline"
                                  className="flex-1 text-xs font-elegant bg-blue-50 hover:bg-blue-100 border-blue-300 text-blue-700"
                                  onClick={() => {
                                    const tableOrder = orders.find(
                                      (o) => o.type === "dine-in" && o.tableNumber === table && o.status !== "completed"
                                    );
                                    if (tableOrder) {
                                      const completedOrder = { ...tableOrder, status: "completed" as const };
                                      saveToDatabase(completedOrder);
                                      setOrders((prevOrders) =>
                                        prevOrders.map((order) =>
                                          order.id === tableOrder.id ? { ...order, status: "completed" as const } : order
                                        )
                                      );
                                      if (currentOrder?.tableNumber === table) {
                                        setCurrentOrder(null);
                                      }
                                      if (selectedTable === table) {
                                        setSelectedTable(null);
                                      }
                                      setTableStartTimes(prev => {
                                        const newTimes = { ...prev };
                                        delete newTimes[table];
                                        return newTimes;
                                      });
                                    }
                                  }}
                                >
                                  💳 دفع
                                </Button>
                              </div>
                              <Button
                                size="sm"
                                variant="outline"
                                className="w-full text-xs font-elegant bg-purple-50 hover:bg-purple-100 border-purple-300 text-purple-700"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  setEditingTableNumber(table);
                                  setIsEditTableOpen(true);
                                }}
                              >
                                ✏️ تعديل الطاولة
                              </Button>
                            </div>
                          )}
                        </div>
                      );
                    })}
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="delivery" className="mt-4">
                <Card>
                  <CardContent className="pt-4">
                    <div className="space-y-4">
                      <div className="space-y-2">
                        <Label htmlFor="name">اسم العميل</Label>
                        <Input
                          id="name"
                          name="name"
                          value={customerInfo.name}
                          onChange={handleCustomerInfoChange}
                          placeholder="أدخل اسم العميل"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="phone">رقم الهاتف</Label>
                        <Input
                          id="phone"
                          name="phone"
                          value={customerInfo.phone}
                          onChange={handleCustomerInfoChange}
                          placeholder="أدخل رقم الهاتف"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="address">عنوان التوصيل</Label>
                        <Input
                          id="address"
                          name="address"
                          value={customerInfo.address}
                          onChange={handleCustomerInfoChange}
                          placeholder="أدخل عنوان التوصيل"
                        />
                      </div>
                      <Button
                        className="w-full"
                        onClick={createDeliveryOrder}
                        disabled={!customerInfo.name || !customerInfo.phone || !customerInfo.address}
                      >
                        إنشاء طلب توصيل
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="takeaway" className="mt-4">
                <Card>
                  <CardContent className="pt-4">
                    <div className="space-y-4">
                      <p>إنشاء طلب سفري جديد</p>
                      <Button className="w-full" onClick={createTakeawayOrder}>
                        إنشاء طلب سفري
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>

            <div className="my-6 h-px" style={{ backgroundColor: '#dee2e6' }}></div>

            <div>
              <div className="mb-4">
                <h4 className="text-md font-bold font-elegant mb-2" style={{ color: '#495057' }}>
                  📋 الطلبات النشطة
                </h4>
                <p className="text-xs" style={{ color: '#6c757d' }}>
                  الطلبات الحالية قيد التنفيذ
                </p>
              </div>
              <ScrollArea className="h-[300px]">
                <div className="space-y-2">
                  {orders
                    .filter((order) => order.status !== "completed")
                    .map((order) => (
                      <Card key={order.id} className="cursor-pointer hover:bg-accent/50">
                        <CardContent className="p-3">
                          <div className="flex justify-between items-center">
                            <div>
                              <div className="font-medium">
                                {order.type === "dine-in"
                                  ? `Table ${order.tableNumber}`
                                  : order.type === "delivery"
                                    ? `Delivery: ${order.customerName}`
                                    : "Takeaway"}
                              </div>
                              <div className="text-sm text-muted-foreground">
                                {new Date(order.createdAt).toLocaleTimeString("ar-SA")} • {order.items.length} عنصر
                              </div>
                            </div>
                            <div className="flex items-center gap-2">
                              {getStatusBadge(order.status)}
                              <ChevronRight className="h-4 w-4 text-muted-foreground" />
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                </div>
              </ScrollArea>
            </div>
          </div>
        )}

        {/* Middle Panel - Order Details */}
        <div className={userType === "cashier" ? "w-1/3 border-r" : "w-1/2 border-r"}>
          {userType === "admin" ? (
            <div className="p-6 h-full" style={{ backgroundColor: '#f8f9fa' }}>
              <div className="text-center">
                <div className="mb-6">
                  <div
                    className="w-20 h-20 mx-auto rounded-full flex items-center justify-center mb-4"
                    style={{ backgroundColor: '#6c757d', color: 'white' }}
                  >
                    <span className="text-2xl font-bold">👨‍💼</span>
                  </div>
                  <h3 className="text-xl font-bold font-elegant" style={{ color: '#495057' }}>
                    لوحة تحكم المدير
                  </h3>
                  <p className="text-sm" style={{ color: '#6c757d' }}>
                    إدارة شاملة للمطعم
                  </p>
                </div>
                <div className="grid grid-cols-1 gap-4">
                  <div className="p-4 rounded-lg border" style={{ backgroundColor: 'white', borderColor: '#dee2e6' }}>
                    <div className="text-2xl mb-2">📊</div>
                    <h4 className="font-semibold font-elegant" style={{ color: '#495057' }}>تقارير المبيعات</h4>
                    <p className="text-xs" style={{ color: '#6c757d' }}>تحليل مفصل مع بحث بالتاريخ</p>
                  </div>
                  <div className="p-4 rounded-lg border" style={{ backgroundColor: 'white', borderColor: '#dee2e6' }}>
                    <div className="text-2xl mb-2">🍽️</div>
                    <h4 className="font-semibold font-elegant" style={{ color: '#495057' }}>إدارة المخزون</h4>
                    <p className="text-xs" style={{ color: '#6c757d' }}>تعديل وإضافة المنتجات</p>
                  </div>
                  <div className="p-4 rounded-lg border" style={{ backgroundColor: 'white', borderColor: '#dee2e6' }}>
                    <div className="text-2xl mb-2">👁️</div>
                    <h4 className="font-semibold font-elegant" style={{ color: '#495057' }}>مراقبة الطلبات</h4>
                    <p className="text-xs" style={{ color: '#6c757d' }}>عرض فقط - لا يمكن إتمام الطلبات</p>
                  </div>
                </div>
                <div className="mt-6 p-4 rounded-lg" style={{ backgroundColor: '#e3f2fd', borderColor: '#2196f3' }}>
                  <div className="text-sm font-medium" style={{ color: '#1976d2' }}>
                    💡 نصيحة: استخدم تبويب "إدارة المخزون" لتعديل المنتجات
                  </div>
                </div>
              </div>
            </div>
          ) : currentOrder ? (
            <OrderDetails
              orderType={currentOrder.type}
              tableNumber={currentOrder.tableNumber}
              orderStatus={currentOrder.status}
              customerInfo={{
                name: currentOrder.customerName || "",
                phone: currentOrder.customerPhone || "",
                address: currentOrder.customerAddress || "",
              }}
              items={currentOrder.items}
              onUpdateStatus={(status) => updateOrderStatus(currentOrder.id, status)}
              onProcessPayment={() => saveOrder()}
              onUpdateQuantity={(itemId, quantity) => {
                setCurrentOrder((prev) => {
                  if (!prev) return null;
                  const updatedItems = prev.items.map((item) =>
                    item.id === itemId ? { ...item, quantity } : item
                  );
                  const total = updatedItems.reduce((sum, item) => sum + item.price * item.quantity, 0);
                  return { ...prev, items: updatedItems, total };
                });
              }}
              onRemoveItem={(itemId) => {
                setCurrentOrder((prev) => {
                  if (!prev) return null;
                  const updatedItems = prev.items.filter((item) => item.id !== itemId);
                  const total = updatedItems.reduce((sum, item) => sum + item.price * item.quantity, 0);
                  return { ...prev, items: updatedItems, total };
                });
              }}
              onPrintOrder={() => printOrder(currentOrder)}
              onPrepareOrder={() => prepareOrder(currentOrder)}
              language={language}
            />
          ) : (
            <div className="flex items-center justify-center h-full text-muted-foreground font-elegant">
              <div className="text-center">
                <div className="text-4xl mb-4">🍽️</div>
                <p>اختر طاولة أو أنشئ طلباً جديداً</p>
              </div>
            </div>
          )}
        </div>

        {/* Right Panel - Product Catalog */}
        <div className={userType === "cashier" ? "w-5/12" : "w-1/2"} style={{ display: 'flex', flexDirection: 'column', height: '100%' }}>
          {userType === "admin" ? (
            <div className="flex flex-col h-full" style={{ backgroundColor: '#f8f9fa' }}>
              <div className="p-6 flex-shrink-0">
                <h3 className="text-xl font-bold font-elegant mb-2" style={{ color: '#495057' }}>
                  📋 عرض المنتجات
                </h3>
                <p className="text-sm" style={{ color: '#6c757d' }}>
                  للتعديل، استخدم تبويب "إدارة المخزون"
                </p>
              </div>
              <div className="flex-1 overflow-hidden">
                <ProductCatalog
                  showInventoryControls={false}
                  onAddToOrder={() => {}}
                  language={language}
                />
              </div>
            </div>
          ) : (
            <div className="flex flex-col h-full" style={{ backgroundColor: '#f8f9fa' }}>
              <div className="p-6 flex-shrink-0">
                <h3 className="text-xl font-bold font-elegant mb-2" style={{ color: '#495057' }}>
                  🛒 كتالوج المنتجات
                </h3>
                <p className="text-sm" style={{ color: '#6c757d' }}>
                  اختر المنتجات لإضافتها للطلب
                </p>
              </div>
              <div className="flex-1 overflow-hidden">
                <ProductCatalog
                  showInventoryControls={false}
                  onAddToOrder={addItemToOrder}
                  language={language}
                />
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Edit Table Dialog */}
      {isEditTableOpen && editingTableNumber && (
        <Dialog open={isEditTableOpen} onOpenChange={setIsEditTableOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>تعديل الطاولة {editingTableNumber}</DialogTitle>
            </DialogHeader>
            <div className="space-y-4">
              <p>سيتم إضافة خيارات تعديل الطاولة قريباً</p>
              <Button onClick={() => setIsEditTableOpen(false)}>
                إغلاق
              </Button>
            </div>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
};

export default OrderManagement;
