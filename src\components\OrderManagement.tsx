import React, { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>bsContent } from "@/components/ui/tabs";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import {
  ChevronRight,
  Plus,
  Trash2,
  CreditCard,
  Receipt,
  Clock,
  CheckCircle,
  AlertCircle,
  Printer,
  Settings,
} from "lucide-react";
import ProductCatalog from "./ProductCatalog";
import OrderDetails from "./OrderDetails";

interface OrderItem {
  id: string;
  name: string;
  price: number;
  quantity: number;
}

interface Order {
  id: string;
  type: "dine-in" | "delivery" | "takeaway";
  tableNumber?: number;
  customerName?: string;
  customerPhone?: string;
  customerAddress?: string;
  items: OrderItem[];
  status: "new" | "in-progress" | "completed";
  total: number;
  createdAt: Date;
}

interface OrderManagementProps {
  language?: "english" | "arabic";
  userType?: "admin" | "cashier";
}

const OrderManagement = ({
  language = "arabic",
  userType = "cashier",
}: OrderManagementProps) => {
  const [activeTab, setActiveTab] = useState<
    "dine-in" | "delivery" | "takeaway"
  >("dine-in");
  const [selectedTable, setSelectedTable] = useState<number | null>(null);
  const [customerInfo, setCustomerInfo] = useState({
    name: "",
    phone: "",
    address: "",
  });
  const [currentOrder, setCurrentOrder] = useState<Order | null>(null);
  const [isTableManagementOpen, setIsTableManagementOpen] = useState(false);
  const [tableCount, setTableCount] = useState(10);
  const [orders, setOrders] = useState<Order[]>([
    {
      id: "1",
      type: "dine-in",
      tableNumber: 1,
      items: [
        { id: "101", name: "Burger", price: 8.99, quantity: 2 },
        { id: "102", name: "Fries", price: 3.99, quantity: 1 },
      ],
      status: "in-progress",
      total: 21.97,
      createdAt: new Date(),
    },
    {
      id: "2",
      type: "delivery",
      customerName: "John Doe",
      customerPhone: "555-1234",
      customerAddress: "123 Main St",
      items: [{ id: "103", name: "Pizza", price: 12.99, quantity: 1 }],
      status: "new",
      total: 12.99,
      createdAt: new Date(),
    },
  ]);

  const tables = Array.from({ length: tableCount }, (_, i) => i + 1);

  // Database simulation - in real app, this would be connected to a database
  const saveToDatabase = (order: Order) => {
    try {
      const existingOrders = JSON.parse(
        localStorage.getItem("restaurant_orders") || "[]",
      );
      const updatedOrders = [
        ...existingOrders,
        { ...order, id: `order-${Date.now()}` },
      ];
      localStorage.setItem("restaurant_orders", JSON.stringify(updatedOrders));
      console.log("Order saved to database:", order);
    } catch (error) {
      console.error("Error saving to database:", error);
    }
  };

  const loadFromDatabase = () => {
    try {
      const savedOrders = JSON.parse(
        localStorage.getItem("restaurant_orders") || "[]",
      );
      setOrders(savedOrders);
    } catch (error) {
      console.error("Error loading from database:", error);
    }
  };

  useEffect(() => {
    loadFromDatabase();
  }, []);

  const handleTableSelect = (tableNumber: number) => {
    setSelectedTable(tableNumber);

    // Check if there's an existing order for this table
    const existingOrder = orders.find(
      (order) =>
        order.type === "dine-in" &&
        order.tableNumber === tableNumber &&
        order.status !== "completed",
    );

    if (existingOrder) {
      setCurrentOrder(existingOrder);
    } else {
      // Create a new order for this table
      const newOrder: Order = {
        id: `order-${Date.now()}`,
        type: "dine-in",
        tableNumber: tableNumber,
        items: [],
        status: "new",
        total: 0,
        createdAt: new Date(),
      };
      setCurrentOrder(newOrder);
    }
  };

  const handleCustomerInfoChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setCustomerInfo((prev) => ({ ...prev, [name]: value }));
  };

  const createDeliveryOrder = () => {
    if (!customerInfo.name || !customerInfo.phone || !customerInfo.address) {
      return; // Validate customer info
    }

    const newOrder: Order = {
      id: `order-${Date.now()}`,
      type: "delivery",
      customerName: customerInfo.name,
      customerPhone: customerInfo.phone,
      customerAddress: customerInfo.address,
      items: [],
      status: "new",
      total: 0,
      createdAt: new Date(),
    };

    setCurrentOrder(newOrder);
  };

  const createTakeawayOrder = () => {
    const newOrder: Order = {
      id: `order-${Date.now()}`,
      type: "takeaway",
      items: [],
      status: "new",
      total: 0,
      createdAt: new Date(),
    };

    setCurrentOrder(newOrder);
  };

  const addItemToOrder = (item: {
    id: string;
    name: string;
    price: number;
  }) => {
    if (!currentOrder) return;

    setCurrentOrder((prevOrder) => {
      if (!prevOrder) return null;

      const existingItem = prevOrder.items.find((i) => i.id === item.id);

      let updatedItems;
      if (existingItem) {
        updatedItems = prevOrder.items.map((i) =>
          i.id === item.id ? { ...i, quantity: i.quantity + 1 } : i,
        );
      } else {
        updatedItems = [...prevOrder.items, { ...item, quantity: 1 }];
      }

      const total = updatedItems.reduce(
        (sum, item) => sum + item.price * item.quantity,
        0,
      );

      return {
        ...prevOrder,
        items: updatedItems,
        total,
      };
    });
  };

  const saveOrder = () => {
    if (!currentOrder || currentOrder.items.length === 0) return;

    // Mark order as completed when processing payment
    const completedOrder = { ...currentOrder, status: "completed" as const };

    // Save to database
    saveToDatabase(completedOrder);

    setOrders((prevOrders) => {
      const orderExists = prevOrders.some(
        (order) => order.id === currentOrder.id,
      );

      if (orderExists) {
        return prevOrders.map((order) =>
          order.id === currentOrder.id ? completedOrder : order,
        );
      } else {
        return [...prevOrders, completedOrder];
      }
    });

    // Always reset current selections regardless of order type
    setSelectedTable(null);
    setCustomerInfo({ name: "", phone: "", address: "" });
    setCurrentOrder(null);
  };

  const prepareOrder = (order: Order) => {
    // Update order status to in-progress
    const preparedOrder = { ...order, status: "in-progress" as const };

    // Save to database
    saveToDatabase(preparedOrder);

    // Update orders state
    setOrders((prevOrders) =>
      prevOrders.map((o) => (o.id === order.id ? preparedOrder : o)),
    );

    // Update current order if it's the same
    if (currentOrder?.id === order.id) {
      setCurrentOrder(preparedOrder);
    }

    // Print preparation receipt
    printPreparationReceipt(preparedOrder);
  };

  const printPreparationReceipt = (order: Order) => {
    const printWindow = window.open("", "_blank");
    if (printWindow) {
      const orderDate = new Date(order.createdAt).toLocaleDateString("ar-SA");
      const orderTime = new Date(order.createdAt).toLocaleTimeString("ar-SA");

      printWindow.document.write(`
        <html dir="rtl">
          <head>
            <title>${language === "english" ? "Kitchen Order" : "طلب المطبخ"}</title>
            <style>
              body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; }
              .header { text-align: center; border-bottom: 2px solid #000; padding-bottom: 10px; background-color: #f0f0f0; }
              .order-info { margin: 20px 0; }
              .items { margin: 20px 0; }
              .kitchen-note { background-color: #fffacd; padding: 10px; border: 2px solid #ffd700; margin: 10px 0; }
              table { width: 100%; border-collapse: collapse; }
              th, td { border: 1px solid #ddd; padding: 12px; text-align: right; }
              th { background-color: #f2f2f2; font-weight: bold; }
              .quantity { font-size: 18px; font-weight: bold; color: #d32f2f; }
            </style>
          </head>
          <body>
            <div class="header">
              <h1>${language === "english" ? "KITCHEN ORDER" : "طلب المطبخ"}</h1>
              <h2>${language === "english" ? "PREPARATION REQUIRED" : "مطلوب التجهيز"}</h2>
            </div>
            <div class="kitchen-note">
              <h3>${language === "english" ? "⚠️ URGENT - PREPARE NOW" : "⚠️ عاجل - جهز الآن"}</h3>
            </div>
            <div class="order-info">
              <p><strong>${language === "english" ? "Order ID:" : "رقم الطلب:"}</strong> ${order.id}</p>
              <p><strong>${language === "english" ? "Date:" : "التاريخ:"}</strong> ${orderDate}</p>
              <p><strong>${language === "english" ? "Time:" : "الوقت:"}</strong> ${orderTime}</p>
              <p><strong>${language === "english" ? "Type:" : "النوع:"}</strong> ${order.type === "dine-in" ? (language === "english" ? "Dine-in" : "تناول في المطعم") : order.type === "delivery" ? (language === "english" ? "Delivery" : "توصيل") : language === "english" ? "Takeaway" : "سفري"}</p>
              ${order.tableNumber ? `<p><strong>${language === "english" ? "Table:" : "الطاولة:"}</strong> ${order.tableNumber}</p>` : ""}
              ${order.customerName ? `<p><strong>${language === "english" ? "Customer:" : "العميل:"}</strong> ${order.customerName}</p>` : ""}
            </div>
            <div class="items">
              <table>
                <thead>
                  <tr>
                    <th>${language === "english" ? "Item" : "الصنف"}</th>
                    <th>${language === "english" ? "Quantity" : "الكمية"}</th>
                    <th>${language === "english" ? "Notes" : "ملاحظات"}</th>
                  </tr>
                </thead>
                <tbody>
                  ${order.items
                    .map(
                      (item) => `
                    <tr>
                      <td>${item.name}</td>
                      <td class="quantity">${item.quantity}</td>
                      <td>-</td>
                    </tr>
                  `,
                    )
                    .join("")}
                </tbody>
              </table>
            </div>
            <div style="text-align: center; margin-top: 30px; border-top: 2px solid #000; padding-top: 20px;">
              <h2>${language === "english" ? "STATUS: IN PREPARATION" : "الحالة: قيد التجهيز"}</h2>
            </div>
          </body>
        </html>
      `);
      printWindow.document.close();
      printWindow.print();
    }
  };

  const printOrder = (order: Order) => {
    const printWindow = window.open("", "_blank");
    if (printWindow) {
      const orderDate = new Date(order.createdAt).toLocaleDateString("ar-SA");
      const orderTime = new Date(order.createdAt).toLocaleTimeString("ar-SA");

      printWindow.document.write(`
        <html dir="rtl">
          <head>
            <title>${language === "english" ? "Order Receipt" : "فاتورة الطلب"}</title>
            <style>
              body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; }
              .header { text-align: center; border-bottom: 2px solid #000; padding-bottom: 10px; }
              .order-info { margin: 20px 0; }
              .items { margin: 20px 0; }
              .total { border-top: 2px solid #000; padding-top: 10px; font-weight: bold; }
              table { width: 100%; border-collapse: collapse; }
              th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }
              th { background-color: #f2f2f2; }
            </style>
          </head>
          <body>
            <div class="header">
              <h1>${language === "english" ? "Restaurant POS System" : "نظام نقاط البيع للمطعم"}</h1>
              <p>${language === "english" ? "Order Receipt" : "فاتورة الطلب"}</p>
            </div>
            <div class="order-info">
              <p><strong>${language === "english" ? "Order ID:" : "رقم الطلب:"}</strong> ${order.id}</p>
              <p><strong>${language === "english" ? "Date:" : "التاريخ:"}</strong> ${orderDate}</p>
              <p><strong>${language === "english" ? "Time:" : "الوقت:"}</strong> ${orderTime}</p>
              <p><strong>${language === "english" ? "Type:" : "النوع:"}</strong> ${order.type === "dine-in" ? (language === "english" ? "Dine-in" : "تناول في المطعم") : order.type === "delivery" ? (language === "english" ? "Delivery" : "توصيل") : language === "english" ? "Takeaway" : "سفري"}</p>
              ${order.tableNumber ? `<p><strong>${language === "english" ? "Table:" : "الطاولة:"}</strong> ${order.tableNumber}</p>` : ""}
              ${order.customerName ? `<p><strong>${language === "english" ? "Customer:" : "العميل:"}</strong> ${order.customerName}</p>` : ""}
            </div>
            <div class="items">
              <table>
                <thead>
                  <tr>
                    <th>${language === "english" ? "Item" : "الصنف"}</th>
                    <th>${language === "english" ? "Quantity" : "الكمية"}</th>
                    <th>${language === "english" ? "Price" : "السعر"}</th>
                    <th>${language === "english" ? "Total" : "المجموع"}</th>
                  </tr>
                </thead>
                <tbody>
                  ${order.items
                    .map(
                      (item) => `
                    <tr>
                      <td>${item.name}</td>
                      <td>${item.quantity}</td>
                      <td>${item.price.toFixed(2)} ${language === "english" ? "SAR" : "ريال"}</td>
                      <td>${(item.price * item.quantity).toFixed(2)} ${language === "english" ? "SAR" : "ريال"}</td>
                    </tr>
                  `,
                    )
                    .join("")}
                </tbody>
              </table>
            </div>
            <div class="total">
              <p><strong>${language === "english" ? "Total Amount:" : "المبلغ الإجمالي:"} ${order.total.toFixed(2)} ${language === "english" ? "SAR" : "ريال"}</strong></p>
            </div>
            <div style="text-align: center; margin-top: 30px;">
              <p>${language === "english" ? "Thank you for your visit!" : "شكراً لزيارتكم!"}</p>
            </div>
          </body>
        </html>
      `);
      printWindow.document.close();
      printWindow.print();
    }
  };

  const updateOrderStatus = (
    orderId: string,
    status: "new" | "in-progress" | "completed",
  ) => {
    setOrders((prevOrders) =>
      prevOrders.map((order) =>
        order.id === orderId ? { ...order, status } : order,
      ),
    );

    if (currentOrder?.id === orderId) {
      setCurrentOrder((prev) => (prev ? { ...prev, status } : null));
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "new":
        return (
          <Badge variant="outline" className="bg-blue-100 text-blue-800">
            New
          </Badge>
        );
      case "in-progress":
        return (
          <Badge variant="outline" className="bg-yellow-100 text-yellow-800">
            In Progress
          </Badge>
        );
      case "completed":
        return (
          <Badge variant="outline" className="bg-green-100 text-green-800">
            Completed
          </Badge>
        );
      default:
        return null;
    }
  };

  return (
    <div className="flex flex-col h-full bg-background">
      <div className="p-4 border-b flex justify-between items-center">
        <h2 className="text-2xl font-bold">إدارة الطلبات</h2>
        {userType === "admin" && (
          <Dialog
            open={isTableManagementOpen}
            onOpenChange={setIsTableManagementOpen}
          >
            <DialogTrigger asChild>
              <Button variant="outline" className="flex items-center gap-2">
                <Settings className="h-4 w-4" />
                إدارة الطاولات
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>إدارة الطاولات</DialogTitle>
              </DialogHeader>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="table-count">عدد الطاولات</Label>
                  <Input
                    id="table-count"
                    type="number"
                    min="1"
                    max="50"
                    value={tableCount}
                    onChange={(e) =>
                      setTableCount(parseInt(e.target.value) || 10)
                    }
                    placeholder="أدخل عدد الطاولات"
                  />
                </div>
                <div className="flex gap-2">
                  <Button
                    onClick={() => setIsTableManagementOpen(false)}
                    className="flex-1"
                  >
                    حفظ التغييرات
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => setIsTableManagementOpen(false)}
                    className="flex-1"
                  >
                    إلغاء
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        )}
      </div>

      <div className="flex flex-1 overflow-hidden">
        {/* Left Panel - Order Type Selection */}
        <div className="w-1/4 border-r p-4">
          <Tabs
            value={activeTab}
            onValueChange={(value) => setActiveTab(value as any)}
          >
            <TabsList className="grid grid-cols-3 w-full">
              <TabsTrigger value="dine-in">تناول في المطعم</TabsTrigger>
              <TabsTrigger value="delivery">توصيل</TabsTrigger>
              <TabsTrigger value="takeaway">سفري</TabsTrigger>
            </TabsList>

            <TabsContent value="dine-in" className="mt-4">
              <h3 className="text-lg font-medium mb-2">اختر الطاولة</h3>
              <div className="grid grid-cols-2 gap-2">
                {tables.map((table) => {
                  const isOccupied = orders.some(
                    (o) =>
                      o.type === "dine-in" &&
                      o.tableNumber === table &&
                      o.status !== "completed",
                  );

                  return (
                    <div key={table} className="space-y-1">
                      <Button
                        variant={
                          selectedTable === table ? "default" : "outline"
                        }
                        className={`h-16 w-full relative ${
                          isOccupied
                            ? "bg-red-100 border-red-300 hover:bg-red-200"
                            : "bg-green-100 border-green-300 hover:bg-green-200"
                        }`}
                        onClick={() => handleTableSelect(table)}
                      >
                        طاولة {table}
                        <div
                          className={`absolute top-1 right-1 w-3 h-3 rounded-full ${
                            isOccupied ? "bg-red-500" : "bg-green-500"
                          }`}
                        ></div>
                      </Button>
                      {isOccupied && (
                        <div className="flex gap-1">
                          <Button
                            size="sm"
                            variant="outline"
                            className="flex-1 text-xs bg-orange-50 hover:bg-orange-100"
                            onClick={() => {
                              const tableOrder = orders.find(
                                (o) =>
                                  o.type === "dine-in" &&
                                  o.tableNumber === table &&
                                  o.status !== "completed",
                              );
                              if (tableOrder) {
                                prepareOrder(tableOrder);
                              }
                            }}
                          >
                            تجهيز
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            className="flex-1 text-xs bg-blue-50 hover:bg-blue-100"
                            onClick={() => {
                              const tableOrder = orders.find(
                                (o) =>
                                  o.type === "dine-in" &&
                                  o.tableNumber === table &&
                                  o.status !== "completed",
                              );
                              if (tableOrder) {
                                // Mark order as completed and save to database
                                const completedOrder = {
                                  ...tableOrder,
                                  status: "completed" as const,
                                };
                                saveToDatabase(completedOrder);

                                // Update orders state to mark as completed
                                setOrders((prevOrders) =>
                                  prevOrders.map((order) =>
                                    order.id === tableOrder.id
                                      ? {
                                          ...order,
                                          status: "completed" as const,
                                        }
                                      : order,
                                  ),
                                );

                                // Force clear everything related to this table
                                if (currentOrder?.tableNumber === table) {
                                  setCurrentOrder(null);
                                }
                                if (selectedTable === table) {
                                  setSelectedTable(null);
                                }
                              }
                            }}
                          >
                            دفع
                          </Button>
                        </div>
                      )}
                    </div>
                  );
                })}
              </div>
            </TabsContent>

            <TabsContent value="delivery" className="mt-4">
              <Card>
                <CardContent className="pt-4">
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="name">اسم العميل</Label>
                      <Input
                        id="name"
                        name="name"
                        value={customerInfo.name}
                        onChange={handleCustomerInfoChange}
                        placeholder="أدخل اسم العميل"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="phone">رقم الهاتف</Label>
                      <Input
                        id="phone"
                        name="phone"
                        value={customerInfo.phone}
                        onChange={handleCustomerInfoChange}
                        placeholder="أدخل رقم الهاتف"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="address">عنوان التوصيل</Label>
                      <Input
                        id="address"
                        name="address"
                        value={customerInfo.address}
                        onChange={handleCustomerInfoChange}
                        placeholder="أدخل عنوان التوصيل"
                      />
                    </div>
                    <Button
                      className="w-full"
                      onClick={createDeliveryOrder}
                      disabled={
                        !customerInfo.name ||
                        !customerInfo.phone ||
                        !customerInfo.address
                      }
                    >
                      إنشاء طلب توصيل
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="takeaway" className="mt-4">
              <Card>
                <CardContent className="pt-4">
                  <div className="space-y-4">
                    <p>إنشاء طلب سفري جديد</p>
                    <Button className="w-full" onClick={createTakeawayOrder}>
                      إنشاء طلب سفري
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>

          <Separator className="my-4" />

          <div>
            <h3 className="text-lg font-medium mb-2">الطلبات النشطة</h3>
            <ScrollArea className="h-[300px]">
              <div className="space-y-2">
                {orders
                  .filter((order) => order.status !== "completed")
                  .map((order) => (
                    <Card
                      key={order.id}
                      className="cursor-pointer hover:bg-accent/50"
                    >
                      <CardContent className="p-3">
                        <div className="flex justify-between items-center">
                          <div>
                            <div className="font-medium">
                              {order.type === "dine-in"
                                ? `Table ${order.tableNumber}`
                                : order.type === "delivery"
                                  ? `Delivery: ${order.customerName}`
                                  : "Takeaway"}
                            </div>
                            <div className="text-sm text-muted-foreground">
                              {new Date(order.createdAt).toLocaleTimeString(
                                "ar-SA",
                              )}{" "}
                              • {order.items.length} عنصر
                            </div>
                          </div>
                          <div className="flex items-center gap-2">
                            {getStatusBadge(order.status)}
                            <ChevronRight className="h-4 w-4 text-muted-foreground" />
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
              </div>
            </ScrollArea>
          </div>
        </div>

        {/* Middle Panel - Order Details */}
        <div className="w-1/3 border-r">
          {currentOrder ? (
            <OrderDetails
              orderType={currentOrder.type}
              tableNumber={currentOrder.tableNumber}
              orderStatus={currentOrder.status}
              customerInfo={{
                name: currentOrder.customerName || "",
                phone: currentOrder.customerPhone || "",
                address: currentOrder.customerAddress || "",
              }}
              items={currentOrder.items}
              onUpdateStatus={(status) =>
                updateOrderStatus(currentOrder.id, status)
              }
              onProcessPayment={() => saveOrder()}
              onUpdateQuantity={(itemId, quantity) => {
                setCurrentOrder((prev) => {
                  if (!prev) return null;
                  const updatedItems = prev.items.map((item) =>
                    item.id === itemId ? { ...item, quantity } : item,
                  );
                  const total = updatedItems.reduce(
                    (sum, item) => sum + item.price * item.quantity,
                    0,
                  );
                  return { ...prev, items: updatedItems, total };
                });
              }}
              onRemoveItem={(itemId) => {
                setCurrentOrder((prev) => {
                  if (!prev) return null;
                  const updatedItems = prev.items.filter(
                    (item) => item.id !== itemId,
                  );
                  const total = updatedItems.reduce(
                    (sum, item) => sum + item.price * item.quantity,
                    0,
                  );
                  return { ...prev, items: updatedItems, total };
                });
              }}
              onPrintOrder={() => printOrder(currentOrder)}
              onPrepareOrder={() => prepareOrder(currentOrder)}
              language={language}
            />
          ) : (
            <div className="flex items-center justify-center h-full text-muted-foreground">
              اختر طاولة أو أنشئ طلباً جديداً
            </div>
          )}
        </div>

        {/* Right Panel - Product Catalog */}
        <div className="w-5/12">
          <ProductCatalog
            onAddToOrder={currentOrder ? addItemToOrder : undefined}
            showInventoryControls={userType === "admin"}
            language={language}
          />
        </div>
      </div>

      {/* Order History Dialog */}
      <Dialog>
        <DialogTrigger asChild>
          <Button
            variant="outline"
            className="absolute bottom-4 right-4 rtl:right-auto rtl:left-4"
          >
            تاريخ الطلبات
          </Button>
        </DialogTrigger>
        <DialogContent className="max-w-4xl">
          <DialogHeader>
            <DialogTitle>تاريخ الطلبات</DialogTitle>
          </DialogHeader>
          <div className="mt-4">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>رقم الطلب</TableHead>
                  <TableHead>النوع</TableHead>
                  <TableHead>التفاصيل</TableHead>
                  <TableHead>العناصر</TableHead>
                  <TableHead>المجموع</TableHead>
                  <TableHead>الحالة</TableHead>
                  <TableHead>الإجراءات</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {orders.map((order) => (
                  <TableRow key={order.id}>
                    <TableCell className="font-medium">{order.id}</TableCell>
                    <TableCell className="capitalize">{order.type}</TableCell>
                    <TableCell>
                      {order.type === "dine-in"
                        ? `Table ${order.tableNumber}`
                        : order.type === "delivery"
                          ? `${order.customerName}, ${order.customerPhone}`
                          : "Takeaway"}
                    </TableCell>
                    <TableCell>{order.items.length}</TableCell>
                    <TableCell>{order.total.toFixed(2)} د.ع</TableCell>
                    <TableCell>{getStatusBadge(order.status)}</TableCell>
                    <TableCell>
                      <div className="flex gap-2">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => printOrder(order)}
                        >
                          <Printer className="h-4 w-4 mr-1 rtl:mr-0 rtl:ml-1" />
                          طباعة
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          className="bg-orange-50 hover:bg-orange-100"
                          onClick={() => prepareOrder(order)}
                          disabled={order.status === "completed"}
                        >
                          <Receipt className="h-4 w-4 mr-1 rtl:mr-0 rtl:ml-1" />
                          تجهيز
                        </Button>
                        <AlertDialog>
                          <AlertDialogTrigger asChild>
                            <Button size="sm" variant="outline">
                              <Clock className="h-4 w-4 mr-1 rtl:mr-0 rtl:ml-1" />
                              الحالة
                            </Button>
                          </AlertDialogTrigger>
                          <AlertDialogContent>
                            <AlertDialogHeader>
                              <AlertDialogTitle>
                                تحديث حالة الطلب
                              </AlertDialogTitle>
                              <AlertDialogDescription>
                                تغيير حالة الطلب {order.id}
                              </AlertDialogDescription>
                            </AlertDialogHeader>
                            <div className="flex flex-col gap-2 py-4">
                              <Button
                                variant={
                                  order.status === "new" ? "default" : "outline"
                                }
                                onClick={() =>
                                  updateOrderStatus(order.id, "new")
                                }
                              >
                                <AlertCircle className="h-4 w-4 mr-2 rtl:mr-0 rtl:ml-2" />
                                جديد
                              </Button>
                              <Button
                                variant={
                                  order.status === "in-progress"
                                    ? "default"
                                    : "outline"
                                }
                                onClick={() =>
                                  updateOrderStatus(order.id, "in-progress")
                                }
                              >
                                <Clock className="h-4 w-4 mr-2 rtl:mr-0 rtl:ml-2" />
                                قيد التنفيذ
                              </Button>
                              <Button
                                variant={
                                  order.status === "completed"
                                    ? "default"
                                    : "outline"
                                }
                                onClick={() =>
                                  updateOrderStatus(order.id, "completed")
                                }
                              >
                                <CheckCircle className="h-4 w-4 mr-2 rtl:mr-0 rtl:ml-2" />
                                مكتمل
                              </Button>
                            </div>
                            <AlertDialogFooter>
                              <AlertDialogCancel>إلغاء</AlertDialogCancel>
                            </AlertDialogFooter>
                          </AlertDialogContent>
                        </AlertDialog>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default OrderManagement;
