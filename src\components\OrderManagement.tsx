import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Plus,
  Clock,
  CheckCircle,
  XCircle,
  ShoppingCart,
  Phone,
  User,
  RefreshCw,
  Printer,
} from "lucide-react";
import { format } from "date-fns";

interface OrderManagementProps {
  language?: "english" | "arabic";
  userType?: "admin" | "cashier";
}

const OrderManagement = ({ language = "arabic", userType = "cashier" }: OrderManagementProps) => {
  const [isNewOrderOpen, setIsNewOrderOpen] = useState(false);

  // Sample orders for display
  const sampleOrders = [
    {
      id: "1",
      orderNumber: "ORD001",
      customerName: "أحمد محمد",
      customerPhone: "0501234567",
      orderType: "dine-in",
      paymentMethod: "cash",
      status: "pending",
      total: 125.50,
      tableNumber: 2,
      createdAt: new Date(),
      items: [
        { name: "كبسة دجاج", quantity: 2, price: 45 },
        { name: "عصير برتقال طازج", quantity: 2, price: 12 },
        { name: "مهلبية", quantity: 1, price: 15 }
      ]
    },
    {
      id: "2",
      orderNumber: "ORD002",
      customerName: "فاطمة علي",
      customerPhone: "0507654321",
      orderType: "delivery",
      paymentMethod: "card",
      status: "preparing",
      total: 89.75,
      deliveryAddress: "شارع الملك فهد، الرياض",
      createdAt: new Date(),
      items: [
        { name: "مندي لحم", quantity: 1, price: 65 },
        { name: "شاي أحمر", quantity: 2, price: 8 },
        { name: "بابا غنوج", quantity: 1, price: 16 }
      ]
    }
  ];

  const getStatusBadge = (status: string) => {
    const statusLabels: { [key: string]: string } = {
      pending: "في الانتظار",
      preparing: "قيد التحضير", 
      ready: "جاهز",
      delivered: "تم التسليم",
      cancelled: "ملغي"
    };
    
    return (
      <Badge variant={
        status === "pending" ? "secondary" :
        status === "preparing" ? "default" :
        status === "ready" ? "outline" :
        status === "delivered" ? "outline" :
        "destructive"
      }>
        {statusLabels[status] || status}
      </Badge>
    );
  };

  const updateOrderStatus = (orderId: string, newStatus: string) => {
    alert(`تم تحديث حالة الطلب ${orderId} إلى: ${newStatus}`);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">إدارة الطلبات</h2>
        <div className="flex gap-2">
          <Button variant="outline">
            <RefreshCw className="h-4 w-4 mr-2" />
            تحديث
          </Button>
          <Button onClick={() => setIsNewOrderOpen(true)}>
            <Plus className="h-4 w-4 mr-2" />
            طلب جديد
          </Button>
        </div>
      </div>

      {/* Sample Orders */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {sampleOrders.map(order => (
          <Card key={order.id} className="fade-in">
            <CardHeader className="pb-3">
              <div className="flex justify-between items-start">
                <div>
                  <CardTitle className="text-lg">{order.orderNumber}</CardTitle>
                  <p className="text-sm text-muted-foreground">
                    {format(order.createdAt, "dd/MM/yyyy HH:mm")}
                  </p>
                </div>
                {getStatusBadge(order.status)}
              </div>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex items-center gap-2">
                <User className="h-4 w-4" />
                <span className="text-sm">{order.customerName}</span>
              </div>

              <div className="flex items-center gap-2">
                <Phone className="h-4 w-4" />
                <span className="text-sm">{order.customerPhone}</span>
              </div>

              <div className="flex items-center gap-2">
                <span className={`px-2 py-1 rounded text-xs ${
                  order.orderType === "dine-in" ? "bg-green-100 text-green-800" :
                  order.orderType === "delivery" ? "bg-blue-100 text-blue-800" :
                  "bg-orange-100 text-orange-800"
                }`}>
                  {order.orderType === "dine-in" ? "تناول في المطعم" :
                   order.orderType === "delivery" ? "توصيل" : "سفري"}
                </span>
                {order.tableNumber && (
                  <span className="text-sm">طاولة {order.tableNumber}</span>
                )}
              </div>

              <div className="text-lg font-bold">
                {order.total.toFixed(2)} ر.س
              </div>

              <div className="flex flex-wrap gap-2">
                {order.status === "pending" && (
                  <Button
                    size="sm"
                    onClick={() => updateOrderStatus(order.id, "preparing")}
                  >
                    <Clock className="h-4 w-4 mr-1" />
                    بدء التحضير
                  </Button>
                )}
                {order.status === "preparing" && (
                  <Button
                    size="sm"
                    onClick={() => updateOrderStatus(order.id, "ready")}
                  >
                    <CheckCircle className="h-4 w-4 mr-1" />
                    جاهز
                  </Button>
                )}
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => alert("سيتم إضافة نظام الطباعة قريباً")}
                >
                  <Printer className="h-4 w-4 mr-1" />
                  طباعة
                </Button>
                <Button
                  size="sm"
                  variant="destructive"
                  onClick={() => updateOrderStatus(order.id, "cancelled")}
                >
                  <XCircle className="h-4 w-4 mr-1" />
                  إلغاء
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* New Order Dialog Placeholder */}
      {isNewOrderOpen && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-xl font-bold">إنشاء طلب جديد</h3>
              <Button variant="outline" onClick={() => setIsNewOrderOpen(false)}>
                ✕
              </Button>
            </div>
            <p className="text-center text-gray-600">
              سيتم إضافة نموذج إنشاء الطلب قريباً
            </p>
            <Button 
              className="w-full mt-4" 
              onClick={() => setIsNewOrderOpen(false)}
            >
              إغلاق
            </Button>
          </div>
        </div>
      )}
    </div>
  );
};

export default OrderManagement;
