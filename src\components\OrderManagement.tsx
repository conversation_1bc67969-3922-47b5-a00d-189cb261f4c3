import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import ProductCatalog from './ProductCatalog';

interface OrderItem {
  id: string;
  name: string;
  price: number;
  quantity: number;
  category: string;
}

interface Order {
  id: string;
  type: 'dine-in' | 'delivery' | 'takeaway';
  tableNumber?: number;
  customerName: string;
  customerPhone: string;
  customerAddress?: string;
  items: OrderItem[];
  total: number;
  status: 'pending' | 'preparing' | 'ready' | 'completed';
  createdAt: Date;
  paymentMethod: 'cash' | 'card' | 'online';
  notes?: string;
}

interface OrderManagementProps {
  userType: 'admin' | 'cashier';
  language: 'ar' | 'en';
}

const OrderManagement: React.FC<OrderManagementProps> = ({ userType, language }) => {
  const [orders, setOrders] = useState<Order[]>([]);
  const [currentOrder, setCurrentOrder] = useState<Order | null>(null);
  const [activeTab, setActiveTab] = useState<'dine-in' | 'delivery' | 'takeaway'>('dine-in');
  const [selectedTable, setSelectedTable] = useState<number | null>(null);
  const [isEditTableOpen, setIsEditTableOpen] = useState(false);
  const [editingTableNumber, setEditingTableNumber] = useState<number | null>(null);
  const [tableStartTimes, setTableStartTimes] = useState<Record<number, Date>>({});
  const [showOpenTables, setShowOpenTables] = useState(false);

  const tables = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10];

  // Load orders from localStorage
  useEffect(() => {
    const savedOrders = localStorage.getItem('restaurant_orders');
    if (savedOrders) {
      const parsedOrders = JSON.parse(savedOrders).map((order: any) => ({
        ...order,
        createdAt: new Date(order.createdAt)
      }));
      setOrders(parsedOrders);
    }

    const savedTableTimes = localStorage.getItem('table_start_times');
    if (savedTableTimes) {
      const parsedTimes = JSON.parse(savedTableTimes);
      const convertedTimes: Record<number, Date> = {};
      Object.keys(parsedTimes).forEach(key => {
        convertedTimes[parseInt(key)] = new Date(parsedTimes[key]);
      });
      setTableStartTimes(convertedTimes);
    }
  }, []);

  const saveToDatabase = (order: Order) => {
    const existingOrders = JSON.parse(localStorage.getItem('restaurant_orders') || '[]');
    const updatedOrders = [...existingOrders, order];
    localStorage.setItem('restaurant_orders', JSON.stringify(updatedOrders));
  };

  const addItemToOrder = (item: any) => {
    if (!currentOrder) {
      const newOrder: Order = {
        id: Date.now().toString(),
        type: activeTab,
        tableNumber: activeTab === 'dine-in' ? selectedTable || undefined : undefined,
        customerName: '',
        customerPhone: '',
        items: [],
        total: 0,
        status: 'pending',
        createdAt: new Date(),
        paymentMethod: 'cash'
      };
      setCurrentOrder(newOrder);
    }

    setCurrentOrder(prev => {
      if (!prev) return null;

      const existingItem = prev.items.find(orderItem => orderItem.id === item.id);
      let updatedItems;

      if (existingItem) {
        updatedItems = prev.items.map(orderItem =>
          orderItem.id === item.id
            ? { ...orderItem, quantity: orderItem.quantity + 1 }
            : orderItem
        );
      } else {
        updatedItems = [...prev.items, { ...item, quantity: 1 }];
      }

      const total = updatedItems.reduce((sum, orderItem) => sum + (orderItem.price * orderItem.quantity), 0);
      const updatedOrder = { ...prev, items: updatedItems, total };

      // Save order to database automatically
      setTimeout(() => {
        saveOrderToDatabase(updatedOrder);
      }, 100);

      return updatedOrder;
    });
  };

  const handleTableSelect = (tableNumber: number) => {
    setSelectedTable(tableNumber);

    const existingOrder = orders.find(
      order => order.type === 'dine-in' && order.tableNumber === tableNumber && order.status !== 'completed'
    );

    if (existingOrder) {
      setCurrentOrder(existingOrder);
    } else {
      // Create new order for empty table
      const newOrder: Order = {
        id: Date.now().toString(),
        type: 'dine-in',
        tableNumber: tableNumber,
        customerName: '',
        customerPhone: '',
        items: [],
        total: 0,
        status: 'pending',
        createdAt: new Date(),
        paymentMethod: 'cash'
      };
      setCurrentOrder(newOrder);
    }
  };

  const prepareOrder = (order: Order) => {
    console.log('🍳 إرسال للمطبخ:', order);
    
    // Update order status to preparing
    setOrders(prevOrders =>
      prevOrders.map(o => 
        o.id === order.id ? { ...o, status: 'preparing' as const } : o
      )
    );

    if (currentOrder?.id === order.id) {
      setCurrentOrder(prev => prev ? { ...prev, status: 'preparing' as const } : null);
    }

    // Show detailed kitchen order
    const itemsList = order.items.map(item => 
      `• ${item.name} × ${item.quantity} (${item.price} ر.س)`
    ).join('\n');

    const orderDetails = `
🍳 تم إرسال الطلب للمطبخ!

📋 تفاصيل الطلب:
🪑 الطاولة: ${order.tableNumber}
👤 العميل: ${order.customerName}
⏰ الوقت: ${new Date().toLocaleTimeString('ar-SA')}

🍽️ المنتجات:
${itemsList}

💰 المجموع: ${order.total.toFixed(2)} ر.س

✅ تم إرسال الطلب لفريق المطبخ للتجهيز!
    `;

    alert(orderDetails);
  };

  const getTableDuration = (tableNumber: number): string | null => {
    const startTime = tableStartTimes[tableNumber];
    if (!startTime) return null;
    
    const now = new Date();
    const diffMs = now.getTime() - startTime.getTime();
    const hours = Math.floor(diffMs / (1000 * 60 * 60));
    const minutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));
    
    return `${hours}س ${minutes}د`;
  };

  // Get occupied tables
  const getOccupiedTables = () => {
    return orders.filter(order =>
      order.type === 'dine-in' &&
      order.status !== 'completed' &&
      order.tableNumber
    );
  };

  // Complete table payment from open tables list
  const completeTablePayment = (tableNumber: number) => {
    const tableOrder = orders.find(
      order => order.type === 'dine-in' && order.tableNumber === tableNumber && order.status !== 'completed'
    );

    if (!tableOrder) {
      alert('لم يتم العثور على طلب لهذه الطاولة');
      return;
    }

    // Show payment confirmation
    const paymentDetails = `
💳 تأكيد دفع الطاولة ${tableNumber}

📋 تفاصيل الطلب:
👤 العميل: ${tableOrder.customerName}
📞 الهاتف: ${tableOrder.customerPhone || 'غير محدد'}

🍽️ المنتجات:
${tableOrder.items.map(item =>
  `• ${item.name} × ${item.quantity} = ${(item.price * item.quantity).toFixed(2)} ر.س`
).join('\n')}

💰 المجموع الفرعي: ${tableOrder.total.toFixed(2)} ر.س
💰 الضريبة (15%): ${(tableOrder.total * 0.15).toFixed(2)} ر.س
💰 المجموع الكلي: ${(tableOrder.total * 1.15).toFixed(2)} ر.س

⏱️ مدة الجلسة: ${getTableDuration(tableNumber) || '0س 0د'}

✅ تم إتمام الدفع بنجاح!
🧹 سيتم تفريغ الطاولة الآن...
    `;

    if (confirm(paymentDetails + '\n\nهل تريد إتمام الدفع وتفريغ الطاولة؟')) {
      // Mark order as completed and save to database
      const completedOrder = { ...tableOrder, status: 'completed' as const, completedAt: new Date() };
      saveToDatabase(completedOrder);

      // Remove order from active orders
      setOrders(prev => prev.filter(order => order.id !== tableOrder.id));

      // Clear table timer
      setTableStartTimes(prev => {
        const newTimes = { ...prev };
        delete newTimes[tableNumber];
        localStorage.setItem('table_start_times', JSON.stringify(newTimes));
        return newTimes;
      });

      // Clear current order if it's the same table
      if (currentOrder?.tableNumber === tableNumber) {
        setCurrentOrder(null);
        setSelectedTable(null);
      }

      alert(`✅ تم إتمام دفع الطاولة ${tableNumber} بنجاح!\n🧹 تم تفريغ الطاولة وهي متاحة الآن للزبائن الجدد! 🎉`);
    }
  };

  // Update item quantity in current order
  const updateCurrentOrderItemQuantity = (itemId: string, newQuantity: number) => {
    if (!currentOrder) return;

    if (newQuantity <= 0) {
      // Remove item if quantity is 0 or less
      const updatedItems = currentOrder.items.filter(item => item.id !== itemId);
      const newTotal = updatedItems.reduce((sum, item) => sum + (item.price * item.quantity), 0);
      const updatedOrder = { ...currentOrder, items: updatedItems, total: newTotal };
      setCurrentOrder(updatedOrder);

      // Save changes automatically
      setTimeout(() => {
        saveOrderToDatabase(updatedOrder);
      }, 100);
    } else {
      // Update quantity
      const updatedItems = currentOrder.items.map(item =>
        item.id === itemId ? { ...item, quantity: newQuantity } : item
      );
      const newTotal = updatedItems.reduce((sum, item) => sum + (item.price * item.quantity), 0);
      const updatedOrder = { ...currentOrder, items: updatedItems, total: newTotal };
      setCurrentOrder(updatedOrder);

      // Save changes automatically
      setTimeout(() => {
        saveOrderToDatabase(updatedOrder);
      }, 100);
    }
  };

  // Remove item from current order
  const removeCurrentOrderItem = (itemId: string) => {
    if (!currentOrder) return;

    const updatedItems = currentOrder.items.filter(item => item.id !== itemId);
    const newTotal = updatedItems.reduce((sum, item) => sum + (item.price * item.quantity), 0);
    const updatedOrder = { ...currentOrder, items: updatedItems, total: newTotal };
    setCurrentOrder(updatedOrder);

    // Save changes automatically
    setTimeout(() => {
      saveOrderToDatabase(updatedOrder);
    }, 100);
  };

  // Save order when first item is added
  const saveOrderToDatabase = (order: Order) => {
    const existingOrderIndex = orders.findIndex(o => o.id === order.id);
    if (existingOrderIndex >= 0) {
      // Update existing order
      setOrders(prev => prev.map(o =>
        o.id === order.id ? order : o
      ));
    } else {
      // Add new order
      setOrders(prev => [...prev, order]);

      // Start timer for dine-in tables
      if (order.type === 'dine-in' && order.tableNumber && order.items.length === 1) {
        setTableStartTimes(prev => ({
          ...prev,
          [order.tableNumber!]: new Date()
        }));
        localStorage.setItem('table_start_times', JSON.stringify({
          ...tableStartTimes,
          [order.tableNumber]: new Date()
        }));
      }
    }
  };

  // Complete payment and clear table
  const completePayment = () => {
    if (!currentOrder) return;

    if (!currentOrder.customerName.trim()) {
      alert('يرجى إدخال اسم العميل');
      return;
    }

    if (currentOrder.items.length === 0) {
      alert('يرجى إضافة عناصر للطلب');
      return;
    }

    // Show payment confirmation
    const paymentDetails = `
💳 تأكيد الدفع

📋 تفاصيل الطلب:
🪑 الطاولة: ${currentOrder.tableNumber}
👤 العميل: ${currentOrder.customerName}
📞 الهاتف: ${currentOrder.customerPhone || 'غير محدد'}

🍽️ المنتجات:
${currentOrder.items.map(item =>
  `• ${item.name} × ${item.quantity} = ${(item.price * item.quantity).toFixed(2)} ر.س`
).join('\n')}

💰 المجموع الفرعي: ${currentOrder.total.toFixed(2)} ر.س
💰 الضريبة (15%): ${(currentOrder.total * 0.15).toFixed(2)} ر.س
💰 المجموع الكلي: ${(currentOrder.total * 1.15).toFixed(2)} ر.س

⏱️ مدة الجلسة: ${currentOrder.tableNumber ? getTableDuration(currentOrder.tableNumber) || '0س 0د' : '0س 0د'}

✅ تم إتمام الدفع بنجاح!
🧹 سيتم تفريغ الطاولة الآن...
    `;

    if (confirm(paymentDetails + '\n\nهل تريد إتمام الدفع وتفريغ الطاولة؟')) {
      // Mark order as completed and save to database
      const completedOrder = { ...currentOrder, status: 'completed' as const, completedAt: new Date() };
      saveToDatabase(completedOrder);

      // Remove order from active orders
      setOrders(prev => prev.filter(order => order.id !== currentOrder.id));

      // Clear table timer
      if (currentOrder.tableNumber) {
        setTableStartTimes(prev => {
          const newTimes = { ...prev };
          delete newTimes[currentOrder.tableNumber!];
          localStorage.setItem('table_start_times', JSON.stringify(newTimes));
          return newTimes;
        });
      }

      // Clear current order and selected table
      setCurrentOrder(null);
      setSelectedTable(null);

      alert('✅ تم إتمام الدفع بنجاح!\n🧹 تم تفريغ الطاولة وهي متاحة الآن للزبائن الجدد! 🎉');
    }
  };

  const completeOrder = () => {
    if (!currentOrder) return;

    if (!currentOrder.customerName.trim()) {
      alert('يرجى إدخال اسم العميل');
      return;
    }

    if (currentOrder.items.length === 0) {
      alert('يرجى إضافة عناصر للطلب');
      return;
    }

    const completedOrder = { ...currentOrder, status: 'completed' as const };
    saveToDatabase(completedOrder);

    // Update orders array
    setOrders(prev => prev.map(order =>
      order.id === currentOrder.id ? completedOrder : order
    ));

    setCurrentOrder(null);
    setSelectedTable(null);
    alert('تم حفظ الطلب بنجاح! 🎉');
  };

  return (
    <div className="flex flex-col h-full" style={{ backgroundColor: '#f8f9fa' }}>
      {/* Enhanced Header */}
      <div
        className="relative overflow-hidden"
        style={{
          background: 'linear-gradient(135deg, #6c757d 0%, #495057 50%, #343a40 100%)',
          borderBottom: '3px solid #dee2e6'
        }}
      >
        <div className="relative p-8">
          <div className="flex justify-between items-center">
            <div className="flex items-center gap-6">
              <div className="relative">
                <div
                  className="w-16 h-16 rounded-full flex items-center justify-center shadow-lg border-4 border-white/20"
                  style={{
                    background: userType === "admin" 
                      ? 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
                      : 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
                  }}
                >
                  <span className="text-2xl">
                    {userType === "admin" ? "👨‍💼" : "👨‍💻"}
                  </span>
                </div>
                <div className="absolute -bottom-1 -right-1 w-6 h-6 rounded-full bg-green-400 border-2 border-white flex items-center justify-center">
                  <div className="w-2 h-2 rounded-full bg-white"></div>
                </div>
              </div>

              <div>
                <h1 className="text-3xl font-bold text-white mb-2 font-elegant">
                  {userType === "admin" ? "🏢 لوحة تحكم المدير" : "🍽️ نظام إدارة الطلبات"}
                </h1>
                <p className="text-white/80 text-lg font-elegant">
                  {userType === "admin" 
                    ? "مراقبة شاملة وتحليل الأداء" 
                    : "إدارة متقدمة للطلبات والطاولات"}
                </p>
              </div>
            </div>

            <div className="flex items-center gap-4">
              <div
                className="px-6 py-4 rounded-xl shadow-lg backdrop-blur-sm border border-white/20"
                style={{ background: 'rgba(255, 255, 255, 0.15)' }}
              >
                <div className="text-center">
                  <div className="text-2xl mb-1">
                    {userType === "admin" ? "📊" : "⚡"}
                  </div>
                  <div className="text-white font-bold text-lg font-elegant">
                    {userType === "admin" ? "مراقب" : "نشط"}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex flex-1 overflow-hidden">
        {/* Left Panel - Order Type Selection (Cashier Only) */}
        {userType === "cashier" && (
          <div
            className="w-1/4 border-r shadow-lg"
            style={{ 
              background: 'linear-gradient(180deg, #ffffff 0%, #f8f9fa 100%)',
              borderColor: '#e9ecef'
            }}
          >
            <div className="p-6 border-b" style={{ borderColor: '#e9ecef' }}>
              <div className="text-center mb-4">
                <h3 className="text-xl font-bold font-elegant mb-2" style={{ color: '#343a40' }}>
                  أنواع الطلبات
                </h3>
              </div>
            </div>

            <div className="p-6">
              <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as any)}>
                <div className="grid grid-cols-1 gap-3 mb-6">
                  {[
                    { value: "dine-in", icon: "🍽️", title: "تناول في المطعم", desc: "طلبات الطاولات" },
                    { value: "delivery", icon: "🚚", title: "توصيل", desc: "طلبات التوصيل" },
                    { value: "takeaway", icon: "📦", title: "سفري", desc: "طلبات الاستلام" }
                  ].map((tab) => (
                    <button
                      key={tab.value}
                      onClick={() => setActiveTab(tab.value as any)}
                      className={`p-4 rounded-xl text-right transition-all duration-300 border-2 ${
                        activeTab === tab.value
                          ? 'shadow-lg transform scale-105'
                          : 'hover:shadow-md hover:transform hover:scale-102'
                      }`}
                      style={{
                        background: activeTab === tab.value
                          ? 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
                          : 'linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%)',
                        borderColor: activeTab === tab.value ? '#667eea' : '#e9ecef',
                        color: activeTab === tab.value ? 'white' : '#495057'
                      }}
                    >
                      <div className="flex items-center gap-3">
                        <div className={`text-2xl ${activeTab === tab.value ? 'animate-pulse' : ''}`}>
                          {tab.icon}
                        </div>
                        <div>
                          <div className="font-bold font-elegant text-lg">{tab.title}</div>
                          <div className={`text-sm font-elegant ${
                            activeTab === tab.value ? 'text-white/80' : 'text-gray-500'
                          }`}>
                            {tab.desc}
                          </div>
                        </div>
                      </div>
                    </button>
                  ))}
                </div>

                <TabsContent value="dine-in" className="mt-6">
                  {/* Tables Grid */}
                  <div className="grid grid-cols-2 gap-4 mb-6">
                    {tables.map((table) => {
                      const isOccupied = orders.some(
                        (o) => o.type === "dine-in" && o.tableNumber === table && o.status !== "completed"
                      );

                      return (
                        <div key={table} className="space-y-2">
                          <div
                            className={`relative p-4 rounded-xl border-2 cursor-pointer transition-all duration-300 ${
                              selectedTable === table
                                ? 'shadow-xl transform scale-105 border-blue-400'
                                : 'hover:shadow-lg hover:transform hover:scale-102'
                            }`}
                            style={{
                              background: selectedTable === table
                                ? 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
                                : isOccupied
                                ? 'linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%)'
                                : 'linear-gradient(135deg, #51cf66 0%, #40c057 100%)',
                              borderColor: selectedTable === table
                                ? '#667eea'
                                : isOccupied
                                ? '#ff6b6b'
                                : '#51cf66'
                            }}
                            onClick={() => handleTableSelect(table)}
                          >
                            <div className="absolute top-2 right-2 flex items-center gap-1">
                              <div className={`w-3 h-3 rounded-full ${
                                isOccupied ? "bg-red-500" : "bg-green-500"
                              } shadow-md`}></div>
                            </div>

                            <div className="text-center">
                              <div className={`text-3xl mb-2 ${selectedTable === table ? 'animate-bounce' : ''}`}>
                                🪑
                              </div>
                              <div className={`font-bold text-lg font-elegant mb-1 ${
                                selectedTable === table ? 'text-white' : 'text-gray-700'
                              }`}>
                                طاولة {table}
                              </div>
                              <div className={`text-sm font-elegant mb-2 ${
                                selectedTable === table ? 'text-white/80' : 'text-gray-600'
                              }`}>
                                {isOccupied ? "🔴 مشغولة" : "🟢 متاحة"}
                              </div>

                              {isOccupied && getTableDuration(table) && (
                                <div className={`text-xs font-mono px-2 py-1 rounded-full ${
                                  selectedTable === table
                                    ? 'bg-white/20 text-white'
                                    : 'bg-gray-700/20 text-gray-700'
                                }`}>
                                  ⏱️ {getTableDuration(table)}
                                </div>
                              )}
                            </div>
                          </div>
                        </div>
                      );
                    })}
                  </div>

                  {/* Open Tables List */}
                  {getOccupiedTables().length > 0 && (
                    <div className="mt-8">
                      <div className="flex items-center justify-between mb-4">
                        <h4 className="text-lg font-bold font-elegant" style={{ color: '#343a40' }}>
                          🔥 الطاولات المفتوحة ({getOccupiedTables().length})
                        </h4>
                        <button
                          onClick={() => setShowOpenTables(!showOpenTables)}
                          className="px-3 py-1 rounded-lg text-sm font-bold transition-all duration-200"
                          style={{
                            background: showOpenTables
                              ? 'linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%)'
                              : 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                            color: 'white',
                            border: 'none'
                          }}
                        >
                          {showOpenTables ? '🔼 إخفاء' : '🔽 عرض'}
                        </button>
                      </div>

                      {showOpenTables && (
                        <div className="space-y-3 max-h-64 overflow-y-auto">
                          {getOccupiedTables().map((order) => (
                            <div
                              key={order.id}
                              className="p-4 rounded-xl border-2 transition-all duration-300 hover:shadow-lg"
                              style={{
                                background: 'linear-gradient(135deg, #fff5f5 0%, #fed7d7 100%)',
                                borderColor: '#ff6b6b'
                              }}
                            >
                              <div className="flex items-center justify-between mb-3">
                                <div className="flex items-center gap-3">
                                  <div
                                    className="w-8 h-8 rounded-full flex items-center justify-center text-white font-bold"
                                    style={{ background: 'linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%)' }}
                                  >
                                    {order.tableNumber}
                                  </div>
                                  <div>
                                    <div className="font-bold font-elegant" style={{ color: '#343a40' }}>
                                      {order.customerName || 'عميل'}
                                    </div>
                                    <div className="text-sm" style={{ color: '#6c757d' }}>
                                      {order.items.length} منتج - {order.total.toFixed(2)} ر.س
                                    </div>
                                  </div>
                                </div>
                                <div className="flex items-center gap-2">
                                  <div
                                    className="px-2 py-1 rounded-full text-xs font-bold"
                                    style={{
                                      background: order.status === 'preparing'
                                        ? 'linear-gradient(135deg, #ff9a56 0%, #ff6b6b 100%)'
                                        : order.status === 'ready'
                                        ? 'linear-gradient(135deg, #51cf66 0%, #40c057 100%)'
                                        : 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                                      color: 'white'
                                    }}
                                  >
                                    {order.status === 'preparing' ? '🍳 قيد التجهيز' :
                                     order.status === 'ready' ? '✅ جاهز' : '📝 جديد'}
                                  </div>
                                  {getTableDuration(order.tableNumber!) && (
                                    <div className="text-xs font-mono px-2 py-1 rounded-full bg-orange-100 text-orange-700">
                                      ⏱️ {getTableDuration(order.tableNumber!)}
                                    </div>
                                  )}
                                </div>
                              </div>

                              <div className="flex gap-2">
                                <button
                                  onClick={() => {
                                    setCurrentOrder(order);
                                    setSelectedTable(order.tableNumber!);
                                  }}
                                  className="flex-1 px-3 py-2 rounded-lg text-sm font-bold transition-all duration-200"
                                  style={{
                                    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                                    color: 'white',
                                    border: 'none'
                                  }}
                                >
                                  ✏️ تعديل
                                </button>

                                {order.status === 'pending' ? (
                                  <button
                                    onClick={() => prepareOrder(order)}
                                    className="flex-1 px-3 py-2 rounded-lg text-sm font-bold transition-all duration-200"
                                    style={{
                                      background: 'linear-gradient(135deg, #ff9a56 0%, #ff6b6b 100%)',
                                      color: 'white',
                                      border: 'none'
                                    }}
                                  >
                                    🍳 تجهيز
                                  </button>
                                ) : (
                                  <button
                                    disabled
                                    className="flex-1 px-3 py-2 rounded-lg text-sm font-bold opacity-50"
                                    style={{
                                      background: 'linear-gradient(135deg, #51cf66 0%, #40c057 100%)',
                                      color: 'white',
                                      border: 'none'
                                    }}
                                  >
                                    ✅ جاهز
                                  </button>
                                )}

                                <button
                                  onClick={() => completeTablePayment(order.tableNumber!)}
                                  className="flex-1 px-3 py-2 rounded-lg text-sm font-bold transition-all duration-200 hover:scale-105"
                                  style={{
                                    background: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
                                    color: 'white',
                                    border: 'none'
                                  }}
                                >
                                  💳 دفع وتفريغ
                                </button>
                              </div>
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  )}
                </TabsContent>

                <TabsContent value="delivery" className="mt-6">
                  <div className="text-center py-8">
                    <div className="text-4xl mb-4">🚚</div>
                    <h4 className="text-lg font-bold font-elegant mb-2" style={{ color: '#343a40' }}>
                      طلبات التوصيل
                    </h4>
                  </div>
                </TabsContent>

                <TabsContent value="takeaway" className="mt-6">
                  <div className="text-center py-8">
                    <div className="text-4xl mb-4">📦</div>
                    <h4 className="text-lg font-bold font-elegant mb-2" style={{ color: '#343a40' }}>
                      طلبات سفري
                    </h4>
                  </div>
                </TabsContent>
              </Tabs>
            </div>
          </div>
        )}

        {/* Middle Panel - Order Details or Admin Dashboard */}
        <div className={userType === "cashier" ? "w-5/12" : "w-1/2"} style={{ backgroundColor: '#f8f9fa' }}>
          {userType === "admin" ? (
            <div className="p-8 h-full">
              <div className="max-w-md mx-auto">
                <div className="text-center mb-8">
                  <div className="relative mb-6">
                    <div
                      className="w-24 h-24 mx-auto rounded-full flex items-center justify-center shadow-xl border-4 border-white"
                      style={{
                        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                        color: 'white'
                      }}
                    >
                      <span className="text-3xl">👨‍💼</span>
                    </div>
                  </div>
                  <h3 className="text-2xl font-bold font-elegant mb-2" style={{ color: '#343a40' }}>
                    🏢 لوحة تحكم المدير
                  </h3>
                  <p className="text-lg font-elegant" style={{ color: '#6c757d' }}>
                    إدارة شاملة ومراقبة متقدمة
                  </p>
                </div>

                <div className="space-y-4 mb-8">
                  {[
                    {
                      icon: "📊",
                      title: "تقارير المبيعات",
                      desc: "تحليل مفصل مع بحث بالتاريخ والوقت",
                      gradient: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)"
                    },
                    {
                      icon: "🍽️",
                      title: "إدارة المخزون",
                      desc: "تعديل وإضافة المنتجات والأسعار",
                      gradient: "linear-gradient(135deg, #f093fb 0%, #f5576c 100%)"
                    },
                    {
                      icon: "👁️",
                      title: "مراقبة الطلبات",
                      desc: "عرض مباشر للطلبات النشطة",
                      gradient: "linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)"
                    }
                  ].map((feature, index) => (
                    <div
                      key={index}
                      className="p-6 rounded-xl shadow-lg border-2 border-gray-200 hover:shadow-xl transition-all duration-300 cursor-pointer transform hover:scale-105"
                      style={{
                        background: 'linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%)',
                      }}
                    >
                      <div className="flex items-center gap-4">
                        <div
                          className="w-12 h-12 rounded-full flex items-center justify-center shadow-md"
                          style={{ background: feature.gradient, color: 'white' }}
                        >
                          <span className="text-xl">{feature.icon}</span>
                        </div>
                        <div className="flex-1">
                          <h4 className="font-bold font-elegant text-lg mb-1" style={{ color: '#343a40' }}>
                            {feature.title}
                          </h4>
                          <p className="text-sm font-elegant" style={{ color: '#6c757d' }}>
                            {feature.desc}
                          </p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          ) : (
            <div className="p-6 h-full">
              {currentOrder ? (
                <div className="h-full flex flex-col">
                  <div className="mb-6">
                    <div className="flex items-center justify-between mb-4">
                      <div>
                        <h3 className="text-xl font-bold font-elegant mb-2" style={{ color: '#343a40' }}>
                          📋 تفاصيل الطلب
                        </h3>
                        <p className="text-sm" style={{ color: '#6c757d' }}>
                          {activeTab === 'dine-in' ? `طاولة ${selectedTable}` :
                           activeTab === 'delivery' ? 'طلب توصيل' : 'طلب سفري'}
                        </p>
                      </div>

                      {currentOrder && (
                        <div
                          className="px-4 py-2 rounded-xl shadow-md"
                          style={{
                            background: currentOrder.status === 'preparing'
                              ? 'linear-gradient(135deg, #ff9a56 0%, #ff6b6b 100%)'
                              : currentOrder.status === 'ready'
                              ? 'linear-gradient(135deg, #51cf66 0%, #40c057 100%)'
                              : 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                            color: 'white'
                          }}
                        >
                          <div className="text-center">
                            <div className="text-lg">
                              {currentOrder.status === 'preparing' ? '🍳' :
                               currentOrder.status === 'ready' ? '✅' : '📝'}
                            </div>
                            <div className="text-sm font-bold font-elegant">
                              {currentOrder.status === 'preparing' ? 'قيد التجهيز' :
                               currentOrder.status === 'ready' ? 'جاهز' : 'جديد'}
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>

                  <div className="space-y-4 mb-6">
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="customerName">اسم العميل *</Label>
                        <Input
                          id="customerName"
                          value={currentOrder.customerName}
                          onChange={(e) => setCurrentOrder(prev =>
                            prev ? { ...prev, customerName: e.target.value } : null
                          )}
                          placeholder="أدخل اسم العميل"
                        />
                      </div>
                      <div>
                        <Label htmlFor="customerPhone">رقم الهاتف</Label>
                        <Input
                          id="customerPhone"
                          value={currentOrder.customerPhone}
                          onChange={(e) => setCurrentOrder(prev =>
                            prev ? { ...prev, customerPhone: e.target.value } : null
                          )}
                          placeholder="أدخل رقم الهاتف"
                        />
                      </div>
                    </div>
                  </div>

                  <div className="flex-1 overflow-hidden">
                    <div className="flex items-center justify-between mb-4">
                      <h4 className="text-lg font-bold font-elegant" style={{ color: '#343a40' }}>
                        🛒 عناصر الطلب ({currentOrder.items.length})
                      </h4>
                      {currentOrder.type === 'dine-in' && currentOrder.tableNumber && getTableDuration(currentOrder.tableNumber) && (
                        <div className="flex items-center gap-2">
                          <div className="text-sm font-bold" style={{ color: '#6c757d' }}>
                            ⏱️ مدة الفتح:
                          </div>
                          <div
                            className="px-3 py-1 rounded-full text-sm font-bold"
                            style={{
                              background: 'linear-gradient(135deg, #ff9a56 0%, #ff6b6b 100%)',
                              color: 'white'
                            }}
                          >
                            {getTableDuration(currentOrder.tableNumber)}
                          </div>
                        </div>
                      )}
                    </div>

                    {currentOrder.items.length > 0 ? (
                      <div className="space-y-3 max-h-64 overflow-y-auto mb-4">
                        {currentOrder.items.map((item, index) => (
                          <div key={index} className="p-4 bg-white rounded-xl shadow-sm border-2 border-gray-100 hover:border-gray-200 transition-all duration-200">
                            <div className="flex justify-between items-center">
                              <div className="flex-1">
                                <div className="font-bold font-elegant text-lg mb-1" style={{ color: '#343a40' }}>
                                  {item.name}
                                </div>
                                <div className="text-sm font-elegant" style={{ color: '#6c757d' }}>
                                  {item.price} ر.س × {item.quantity} = {(item.price * item.quantity).toFixed(2)} ر.س
                                </div>
                              </div>

                              <div className="flex items-center gap-2 ml-4">
                                {/* Decrease Quantity */}
                                <button
                                  onClick={() => updateCurrentOrderItemQuantity(item.id, item.quantity - 1)}
                                  className="w-8 h-8 rounded-full flex items-center justify-center text-white font-bold transition-all duration-200 hover:scale-110"
                                  style={{
                                    background: 'linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%)',
                                    border: 'none'
                                  }}
                                >
                                  -
                                </button>

                                {/* Quantity Display */}
                                <div
                                  className="w-12 h-8 rounded-lg flex items-center justify-center font-bold text-lg"
                                  style={{
                                    background: 'linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%)',
                                    color: '#343a40',
                                    border: '2px solid #dee2e6'
                                  }}
                                >
                                  {item.quantity}
                                </div>

                                {/* Increase Quantity */}
                                <button
                                  onClick={() => updateCurrentOrderItemQuantity(item.id, item.quantity + 1)}
                                  className="w-8 h-8 rounded-full flex items-center justify-center text-white font-bold transition-all duration-200 hover:scale-110"
                                  style={{
                                    background: 'linear-gradient(135deg, #51cf66 0%, #40c057 100%)',
                                    border: 'none'
                                  }}
                                >
                                  +
                                </button>

                                {/* Remove Item */}
                                <button
                                  onClick={() => removeCurrentOrderItem(item.id)}
                                  className="w-8 h-8 rounded-full flex items-center justify-center text-white font-bold transition-all duration-200 hover:scale-110 ml-2"
                                  style={{
                                    background: 'linear-gradient(135deg, #dc3545 0%, #c82333 100%)',
                                    border: 'none'
                                  }}
                                >
                                  ×
                                </button>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <div className="text-center py-12">
                        <div className="text-6xl mb-4">🍽️</div>
                        <h5 className="text-xl font-bold font-elegant mb-2" style={{ color: '#343a40' }}>
                          لا توجد عناصر في الطلب
                        </h5>
                        <p className="text-sm font-elegant" style={{ color: '#6c757d' }}>
                          اختر المنتجات من الكتالوج لإضافتها للطلب
                        </p>
                      </div>
                    )}

                    {currentOrder.items.length > 0 && (
                      <div className="border-t pt-4 space-y-2">
                        <div className="flex justify-between text-lg">
                          <span>المجموع الفرعي:</span>
                          <span>{currentOrder.total.toFixed(2)} ر.س</span>
                        </div>
                        <div className="flex justify-between text-lg">
                          <span>الضريبة (15%):</span>
                          <span>{(currentOrder.total * 0.15).toFixed(2)} ر.س</span>
                        </div>
                        <div className="flex justify-between text-xl font-bold" style={{ color: '#28a745' }}>
                          <span>المجموع الكلي:</span>
                          <span>{(currentOrder.total * 1.15).toFixed(2)} ر.س</span>
                        </div>
                      </div>
                    )}
                  </div>

                  <div className="mt-6 space-y-3">
                    {/* Prepare Order Button */}
                    {currentOrder.items.length > 0 && currentOrder.status === 'pending' && (
                      <button
                        onClick={() => prepareOrder(currentOrder)}
                        className="w-full px-4 py-3 rounded-xl text-lg font-bold font-elegant transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105"
                        style={{
                          background: 'linear-gradient(135deg, #ff9a56 0%, #ff6b6b 100%)',
                          color: 'white',
                          border: 'none'
                        }}
                      >
                        🍳 تجهيز الطلب للمطبخ
                      </button>
                    )}

                    {/* Payment Button */}
                    <button
                      onClick={completePayment}
                      disabled={!currentOrder.customerName.trim() || currentOrder.items.length === 0}
                      className="w-full px-4 py-3 rounded-xl text-lg font-bold font-elegant transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
                      style={{
                        background: currentOrder.customerName.trim() && currentOrder.items.length > 0
                          ? 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)'
                          : 'linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%)',
                        color: currentOrder.customerName.trim() && currentOrder.items.length > 0 ? 'white' : '#6c757d',
                        border: 'none'
                      }}
                    >
                      💳 إتمام الدفع وتفريغ الطاولة
                    </button>
                  </div>
                </div>
              ) : (
                <div className="h-full flex items-center justify-center">
                  <div className="text-center">
                    <div className="text-6xl mb-4">🍽️</div>
                    <h3 className="text-xl font-bold font-elegant mb-2" style={{ color: '#343a40' }}>
                      اختر طاولة أو ابدأ طلب جديد
                    </h3>
                    <p className="text-sm" style={{ color: '#6c757d' }}>
                      اختر طاولة من اليسار أو ابدأ طلب توصيل/سفري
                    </p>
                  </div>
                </div>
              )}
            </div>
          )}
        </div>

        {/* Right Panel - Product Catalog */}
        <div className={userType === "cashier" ? "w-5/12" : "w-1/2"} style={{ display: 'flex', flexDirection: 'column', height: '100%' }}>
          {userType === "admin" ? (
            <div className="flex flex-col h-full" style={{ backgroundColor: '#f8f9fa' }}>
              <div className="p-6 flex-shrink-0 border-b" style={{ borderColor: '#e9ecef' }}>
                <div className="text-center">
                  <h3 className="text-xl font-bold font-elegant mb-2" style={{ color: '#343a40' }}>
                    عرض المنتجات
                  </h3>
                  <p className="text-sm font-elegant" style={{ color: '#6c757d' }}>
                    للتعديل، استخدم تبويب "إدارة المخزون"
                  </p>
                </div>
              </div>
              <div className="flex-1 overflow-hidden">
                <ProductCatalog
                  showInventoryControls={false}
                  onAddToOrder={() => {}}
                  language={language}
                />
              </div>
            </div>
          ) : (
            <div className="flex flex-col h-full" style={{ backgroundColor: '#f8f9fa' }}>
              <div className="p-6 flex-shrink-0 border-b" style={{ borderColor: '#e9ecef' }}>
                <div className="text-center">
                  <h3 className="text-xl font-bold font-elegant mb-2" style={{ color: '#343a40' }}>
                    كتالوج المنتجات
                  </h3>
                  <p className="text-sm font-elegant" style={{ color: '#6c757d' }}>
                    اختر المنتجات لإضافتها للطلب
                  </p>
                </div>
              </div>
              <div className="flex-1 overflow-hidden">
                <ProductCatalog
                  showInventoryControls={false}
                  onAddToOrder={addItemToOrder}
                  language={language}
                />
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Edit Table Dialog */}
      <Dialog open={isEditTableOpen} onOpenChange={setIsEditTableOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>تعديل طاولة {editingTableNumber}</DialogTitle>
          </DialogHeader>
          <div className="text-center py-8">
            <div className="text-4xl mb-4">🔧</div>
            <p>سيتم إضافة خيارات تعديل الطاولة قريباً</p>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default OrderManagement;
