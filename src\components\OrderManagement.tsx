import React, { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>bsContent } from "@/components/ui/tabs";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import {
  ChevronRight,
  Plus,
  Trash2,
  CreditCard,
  Receipt,
  Clock,
  CheckCircle,
  AlertCircle,
  Printer,
  Settings,
} from "lucide-react";
import ProductCatalog from "./ProductCatalog";
import OrderDetails from "./OrderDetails";

interface OrderItem {
  id: string;
  name: string;
  price: number;
  quantity: number;
}

interface Order {
  id: string;
  type: "dine-in" | "delivery" | "takeaway";
  tableNumber?: number;
  customerName?: string;
  customerPhone?: string;
  customerAddress?: string;
  items: OrderItem[];
  status: "new" | "in-progress" | "completed";
  total: number;
  createdAt: Date;
}

interface OrderManagementProps {
  language?: "english" | "arabic";
  userType?: "admin" | "cashier";
}

const OrderManagement = ({
  language = "arabic",
  userType = "cashier",
}: OrderManagementProps) => {
  const [activeTab, setActiveTab] = useState<
    "dine-in" | "delivery" | "takeaway"
  >("dine-in");
  const [selectedTable, setSelectedTable] = useState<number | null>(null);
  const [customerInfo, setCustomerInfo] = useState({
    name: "",
    phone: "",
    address: "",
  });
  const [currentOrder, setCurrentOrder] = useState<Order | null>(null);
  const [isTableManagementOpen, setIsTableManagementOpen] = useState(false);
  const [tableCount, setTableCount] = useState(10);
  const [tableStartTimes, setTableStartTimes] = useState<{[key: number]: Date}>({});
  const [isEditTableOpen, setIsEditTableOpen] = useState(false);
  const [editingTableNumber, setEditingTableNumber] = useState<number | null>(null);
  const [orders, setOrders] = useState<Order[]>([
    {
      id: "1",
      type: "dine-in",
      tableNumber: 1,
      items: [
        { id: "101", name: "Burger", price: 8.99, quantity: 2 },
        { id: "102", name: "Fries", price: 3.99, quantity: 1 },
      ],
      status: "in-progress",
      total: 21.97,
      createdAt: new Date(),
    },
    {
      id: "2",
      type: "delivery",
      customerName: "John Doe",
      customerPhone: "555-1234",
      customerAddress: "123 Main St",
      items: [{ id: "103", name: "Pizza", price: 12.99, quantity: 1 }],
      status: "new",
      total: 12.99,
      createdAt: new Date(),
    },
  ]);

  const tables = Array.from({ length: tableCount }, (_, i) => i + 1);

  // Database simulation - in real app, this would be connected to a database
  const saveToDatabase = (order: Order) => {
    try {
      const existingOrders = JSON.parse(
        localStorage.getItem("restaurant_orders") || "[]",
      );
      const updatedOrders = [
        ...existingOrders,
        { ...order, id: `order-${Date.now()}` },
      ];
      localStorage.setItem("restaurant_orders", JSON.stringify(updatedOrders));
      console.log("Order saved to database:", order);
    } catch (error) {
      console.error("Error saving to database:", error);
    }
  };

  const loadFromDatabase = () => {
    try {
      const savedOrders = JSON.parse(
        localStorage.getItem("restaurant_orders") || "[]",
      );
      setOrders(savedOrders);
    } catch (error) {
      console.error("Error loading from database:", error);
    }
  };

  useEffect(() => {
    loadFromDatabase();
  }, []);

  const handleTableSelect = (tableNumber: number) => {
    setSelectedTable(tableNumber);

    // Check if there's an existing order for this table
    const existingOrder = orders.find(
      (order) =>
        order.type === "dine-in" &&
        order.tableNumber === tableNumber &&
        order.status !== "completed",
    );

    if (existingOrder) {
      setCurrentOrder(existingOrder);
    } else {
      // Create a new order for this table and start timing
      const newOrder: Order = {
        id: `order-${Date.now()}`,
        type: "dine-in",
        tableNumber: tableNumber,
        items: [],
        status: "new",
        total: 0,
        createdAt: new Date(),
      };
      setCurrentOrder(newOrder);

      // Start timing for this table
      setTableStartTimes(prev => ({
        ...prev,
        [tableNumber]: new Date()
      }));
    }
  };

  // Function to get table duration
  const getTableDuration = (tableNumber: number) => {
    const startTime = tableStartTimes[tableNumber];
    if (!startTime) return null;

    const now = new Date();
    const diffMs = now.getTime() - startTime.getTime();
    const diffMins = Math.floor(diffMs / 60000);
    const hours = Math.floor(diffMins / 60);
    const minutes = diffMins % 60;

    if (hours > 0) {
      return `${hours}س ${minutes}د`;
    }
    return `${minutes}د`;
  };

  const handleCustomerInfoChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setCustomerInfo((prev) => ({ ...prev, [name]: value }));
  };

  const createDeliveryOrder = () => {
    if (!customerInfo.name || !customerInfo.phone || !customerInfo.address) {
      return; // Validate customer info
    }

    const newOrder: Order = {
      id: `order-${Date.now()}`,
      type: "delivery",
      customerName: customerInfo.name,
      customerPhone: customerInfo.phone,
      customerAddress: customerInfo.address,
      items: [],
      status: "new",
      total: 0,
      createdAt: new Date(),
    };

    setCurrentOrder(newOrder);
  };

  const createTakeawayOrder = () => {
    const newOrder: Order = {
      id: `order-${Date.now()}`,
      type: "takeaway",
      items: [],
      status: "new",
      total: 0,
      createdAt: new Date(),
    };

    setCurrentOrder(newOrder);
  };

  const addItemToOrder = (item: {
    id: string;
    name: string;
    price: number;
  }) => {
    if (!currentOrder) return;

    setCurrentOrder((prevOrder) => {
      if (!prevOrder) return null;

      const existingItem = prevOrder.items.find((i) => i.id === item.id);

      let updatedItems;
      if (existingItem) {
        updatedItems = prevOrder.items.map((i) =>
          i.id === item.id ? { ...i, quantity: i.quantity + 1 } : i,
        );
      } else {
        updatedItems = [...prevOrder.items, { ...item, quantity: 1 }];
      }

      const total = updatedItems.reduce(
        (sum, item) => sum + item.price * item.quantity,
        0,
      );

      return {
        ...prevOrder,
        items: updatedItems,
        total,
      };
    });
  };

  const saveOrder = () => {
    if (!currentOrder || currentOrder.items.length === 0) return;

    // Mark order as completed when processing payment
    const completedOrder = { ...currentOrder, status: "completed" as const };

    // Save to database
    saveToDatabase(completedOrder);

    setOrders((prevOrders) => {
      const orderExists = prevOrders.some(
        (order) => order.id === currentOrder.id,
      );

      if (orderExists) {
        return prevOrders.map((order) =>
          order.id === currentOrder.id ? completedOrder : order,
        );
      } else {
        return [...prevOrders, completedOrder];
      }
    });

    // Always reset current selections regardless of order type
    setSelectedTable(null);
    setCustomerInfo({ name: "", phone: "", address: "" });
    setCurrentOrder(null);
  };

  const prepareOrder = (order: Order) => {
    // Update order status to in-progress
    const preparedOrder = { ...order, status: "in-progress" as const };

    // Save to database
    saveToDatabase(preparedOrder);

    // Update orders state
    setOrders((prevOrders) =>
      prevOrders.map((o) => (o.id === order.id ? preparedOrder : o)),
    );

    // Update current order if it's the same
    if (currentOrder?.id === order.id) {
      setCurrentOrder(preparedOrder);
    }

    // Print preparation receipt
    printPreparationReceipt(preparedOrder);
  };

  const printPreparationReceipt = (order: Order) => {
    const printWindow = window.open("", "_blank");
    if (printWindow) {
      const orderDate = new Date(order.createdAt).toLocaleDateString("ar-SA");
      const orderTime = new Date(order.createdAt).toLocaleTimeString("ar-SA");

      printWindow.document.write(`
        <html dir="rtl">
          <head>
            <title>${language === "english" ? "Kitchen Order" : "طلب المطبخ"}</title>
            <style>
              body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; }
              .header { text-align: center; border-bottom: 2px solid #000; padding-bottom: 10px; background-color: #f0f0f0; }
              .order-info { margin: 20px 0; }
              .items { margin: 20px 0; }
              .kitchen-note { background-color: #fffacd; padding: 10px; border: 2px solid #ffd700; margin: 10px 0; }
              table { width: 100%; border-collapse: collapse; }
              th, td { border: 1px solid #ddd; padding: 12px; text-align: right; }
              th { background-color: #f2f2f2; font-weight: bold; }
              .quantity { font-size: 18px; font-weight: bold; color: #d32f2f; }
            </style>
          </head>
          <body>
            <div class="header">
              <h1>${language === "english" ? "KITCHEN ORDER" : "طلب المطبخ"}</h1>
              <h2>${language === "english" ? "PREPARATION REQUIRED" : "مطلوب التجهيز"}</h2>
            </div>
            <div class="kitchen-note">
              <h3>${language === "english" ? "⚠️ URGENT - PREPARE NOW" : "⚠️ عاجل - جهز الآن"}</h3>
            </div>
            <div class="order-info">
              <p><strong>${language === "english" ? "Order ID:" : "رقم الطلب:"}</strong> ${order.id}</p>
              <p><strong>${language === "english" ? "Date:" : "التاريخ:"}</strong> ${orderDate}</p>
              <p><strong>${language === "english" ? "Time:" : "الوقت:"}</strong> ${orderTime}</p>
              <p><strong>${language === "english" ? "Type:" : "النوع:"}</strong> ${order.type === "dine-in" ? (language === "english" ? "Dine-in" : "تناول في المطعم") : order.type === "delivery" ? (language === "english" ? "Delivery" : "توصيل") : language === "english" ? "Takeaway" : "سفري"}</p>
              ${order.tableNumber ? `<p><strong>${language === "english" ? "Table:" : "الطاولة:"}</strong> ${order.tableNumber}</p>` : ""}
              ${order.customerName ? `<p><strong>${language === "english" ? "Customer:" : "العميل:"}</strong> ${order.customerName}</p>` : ""}
            </div>
            <div class="items">
              <table>
                <thead>
                  <tr>
                    <th>${language === "english" ? "Item" : "الصنف"}</th>
                    <th>${language === "english" ? "Quantity" : "الكمية"}</th>
                    <th>${language === "english" ? "Notes" : "ملاحظات"}</th>
                  </tr>
                </thead>
                <tbody>
                  ${order.items
                    .map(
                      (item) => `
                    <tr>
                      <td>${item.name}</td>
                      <td class="quantity">${item.quantity}</td>
                      <td>-</td>
                    </tr>
                  `,
                    )
                    .join("")}
                </tbody>
              </table>
            </div>
            <div style="text-align: center; margin-top: 30px; border-top: 2px solid #000; padding-top: 20px;">
              <h2>${language === "english" ? "STATUS: IN PREPARATION" : "الحالة: قيد التجهيز"}</h2>
            </div>
          </body>
        </html>
      `);
      printWindow.document.close();
      printWindow.print();
    }
  };

  const printOrder = (order: Order) => {
    const printWindow = window.open("", "_blank");
    if (printWindow) {
      const orderDate = new Date(order.createdAt).toLocaleDateString("ar-SA");
      const orderTime = new Date(order.createdAt).toLocaleTimeString("ar-SA");

      printWindow.document.write(`
        <html dir="rtl">
          <head>
            <title>${language === "english" ? "Order Receipt" : "فاتورة الطلب"}</title>
            <style>
              body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; }
              .header { text-align: center; border-bottom: 2px solid #000; padding-bottom: 10px; }
              .order-info { margin: 20px 0; }
              .items { margin: 20px 0; }
              .total { border-top: 2px solid #000; padding-top: 10px; font-weight: bold; }
              table { width: 100%; border-collapse: collapse; }
              th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }
              th { background-color: #f2f2f2; }
            </style>
          </head>
          <body>
            <div class="header">
              <h1>${language === "english" ? "Restaurant POS System" : "نظام نقاط البيع للمطعم"}</h1>
              <p>${language === "english" ? "Order Receipt" : "فاتورة الطلب"}</p>
            </div>
            <div class="order-info">
              <p><strong>${language === "english" ? "Order ID:" : "رقم الطلب:"}</strong> ${order.id}</p>
              <p><strong>${language === "english" ? "Date:" : "التاريخ:"}</strong> ${orderDate}</p>
              <p><strong>${language === "english" ? "Time:" : "الوقت:"}</strong> ${orderTime}</p>
              <p><strong>${language === "english" ? "Type:" : "النوع:"}</strong> ${order.type === "dine-in" ? (language === "english" ? "Dine-in" : "تناول في المطعم") : order.type === "delivery" ? (language === "english" ? "Delivery" : "توصيل") : language === "english" ? "Takeaway" : "سفري"}</p>
              ${order.tableNumber ? `<p><strong>${language === "english" ? "Table:" : "الطاولة:"}</strong> ${order.tableNumber}</p>` : ""}
              ${order.customerName ? `<p><strong>${language === "english" ? "Customer:" : "العميل:"}</strong> ${order.customerName}</p>` : ""}
            </div>
            <div class="items">
              <table>
                <thead>
                  <tr>
                    <th>${language === "english" ? "Item" : "الصنف"}</th>
                    <th>${language === "english" ? "Quantity" : "الكمية"}</th>
                    <th>${language === "english" ? "Price" : "السعر"}</th>
                    <th>${language === "english" ? "Total" : "المجموع"}</th>
                  </tr>
                </thead>
                <tbody>
                  ${order.items
                    .map(
                      (item) => `
                    <tr>
                      <td>${item.name}</td>
                      <td>${item.quantity}</td>
                      <td>${item.price.toFixed(2)} ${language === "english" ? "SAR" : "ريال"}</td>
                      <td>${(item.price * item.quantity).toFixed(2)} ${language === "english" ? "SAR" : "ريال"}</td>
                    </tr>
                  `,
                    )
                    .join("")}
                </tbody>
              </table>
            </div>
            <div class="total">
              <p><strong>${language === "english" ? "Total Amount:" : "المبلغ الإجمالي:"} ${order.total.toFixed(2)} ${language === "english" ? "SAR" : "ريال"}</strong></p>
            </div>
            <div style="text-align: center; margin-top: 30px;">
              <p>${language === "english" ? "Thank you for your visit!" : "شكراً لزيارتكم!"}</p>
            </div>
          </body>
        </html>
      `);
      printWindow.document.close();
      printWindow.print();
    }
  };

  const updateOrderStatus = (
    orderId: string,
    status: "new" | "in-progress" | "completed",
  ) => {
    setOrders((prevOrders) =>
      prevOrders.map((order) =>
        order.id === orderId ? { ...order, status } : order,
      ),
    );

    if (currentOrder?.id === orderId) {
      setCurrentOrder((prev) => (prev ? { ...prev, status } : null));
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "new":
        return (
          <Badge variant="outline" className="bg-blue-100 text-blue-800">
            New
          </Badge>
        );
      case "in-progress":
        return (
          <Badge variant="outline" className="bg-yellow-100 text-yellow-800">
            In Progress
          </Badge>
        );
      case "completed":
        return (
          <Badge variant="outline" className="bg-green-100 text-green-800">
            Completed
          </Badge>
        );
      default:
        return null;
    }
  };

  return (
    <div className="flex flex-col h-full bg-background">
      <div
        className="p-6 border-b flex justify-between items-center"
        style={{ backgroundColor: '#e9ecef', borderColor: '#dee2e6' }}
      >
        <div className="flex items-center gap-4">
          <div
            className="w-12 h-12 rounded-full flex items-center justify-center"
            style={{ backgroundColor: '#6c757d', color: 'white' }}
          >
            <span className="text-xl">
              {userType === "admin" ? "👨‍💼" : "👨‍💻"}
            </span>
          </div>
          <div>
            <h2 className="text-2xl font-bold font-elegant" style={{ color: '#343a40' }}>
              إدارة الطلبات
            </h2>
            <p className="text-sm" style={{ color: '#6c757d' }}>
              {userType === "admin" ? "وضع المدير - عرض ومراقبة" : "وضع الكاشير - إدارة كاملة"}
            </p>
          </div>
        </div>

        <div className="flex items-center gap-3">
          <div
            className="px-4 py-2 rounded-lg"
            style={{
              backgroundColor: userType === "admin" ? '#e3f2fd' : '#e8f5e8',
              color: userType === "admin" ? '#1976d2' : '#388e3c'
            }}
          >
            <span className="text-sm font-medium font-elegant">
              {userType === "admin" ? "🔍 مراقب" : "⚡ نشط"}
            </span>
          </div>

          <div className="text-right">
            <div className="text-sm font-medium" style={{ color: '#495057' }}>
              {new Date().toLocaleDateString('ar-SA')}
            </div>
            <div className="text-xs" style={{ color: '#6c757d' }}>
              {new Date().toLocaleTimeString('ar-SA')}
            </div>
          </div>
        </div>
      </div>

      <div className="flex flex-1 overflow-hidden">
        {/* Left Panel - Order Type Selection (Cashier Only) */}
        {userType === "cashier" && (
          <div
            className="w-1/4 border-r p-6"
            style={{ backgroundColor: '#f8f9fa', borderColor: '#dee2e6' }}
          >
          <div className="mb-6">
            <h3 className="text-lg font-bold font-elegant mb-2" style={{ color: '#495057' }}>
              🍽️ أنواع الطلبات
            </h3>
            <p className="text-sm mb-4" style={{ color: '#6c757d' }}>
              اختر نوع الطلب لبدء العمل
            </p>
          </div>

          <Tabs
            value={activeTab}
            onValueChange={(value) => setActiveTab(value as any)}
          >
            <TabsList
              className="grid grid-cols-3 w-full p-1"
              style={{ backgroundColor: '#e9ecef' }}
            >
              <TabsTrigger
                value="dine-in"
                className="font-elegant text-sm"
                style={{ color: '#495057' }}
              >
                🍽️ تناول في المطعم
              </TabsTrigger>
              <TabsTrigger
                value="delivery"
                className="font-elegant text-sm"
                style={{ color: '#495057' }}
              >
                🚚 توصيل
              </TabsTrigger>
              <TabsTrigger
                value="takeaway"
                className="font-elegant text-sm"
                style={{ color: '#495057' }}
              >
                📦 سفري
              </TabsTrigger>
            </TabsList>

            <TabsContent value="dine-in" className="mt-6 flex flex-col h-full">
              <div className="mb-4 flex-shrink-0">
                <h4 className="text-md font-bold font-elegant mb-2" style={{ color: '#495057' }}>
                  🪑 اختر الطاولة
                </h4>
                <p className="text-xs" style={{ color: '#6c757d' }}>
                  الطاولات الخضراء متاحة، الحمراء مشغولة
                </p>
              </div>

              {/* Fixed Tables Section */}
              <div className="flex-shrink-0 mb-4">
                <div className="grid grid-cols-2 gap-3">
                {tables.map((table) => {
                  const isOccupied = orders.some(
                    (o) =>
                      o.type === "dine-in" &&
                      o.tableNumber === table &&
                      o.status !== "completed",
                  );

                  return (
                    <div key={table} className="space-y-1">
                      <Button
                        variant={
                          selectedTable === table ? "default" : "outline"
                        }
                        className={`h-24 w-full relative font-elegant transition-all duration-200 ${
                          selectedTable === table
                            ? "bg-blue-500 text-white border-blue-500 shadow-lg"
                            : isOccupied
                            ? "bg-red-50 border-red-300 hover:bg-red-100 text-red-700"
                            : "bg-green-50 border-green-300 hover:bg-green-100 text-green-700"
                        }`}
                        onClick={() => handleTableSelect(table)}
                      >
                        <div className="text-center w-full">
                          <div className="text-lg font-bold">🪑</div>
                          <div className="text-sm font-medium">طاولة {table}</div>
                          <div className="text-xs">
                            {isOccupied ? "مشغولة" : "متاحة"}
                          </div>
                          {isOccupied && getTableDuration(table) && (
                            <div className="text-xs font-mono mt-1 px-1 py-0.5 rounded"
                                 style={{ backgroundColor: 'rgba(255,255,255,0.2)' }}>
                              ⏱️ {getTableDuration(table)}
                            </div>
                          )}
                        </div>
                        <div
                          className={`absolute top-2 right-2 w-3 h-3 rounded-full ${
                            isOccupied ? "bg-red-500" : "bg-green-500"
                          }`}
                        ></div>
                      </Button>
                      {isOccupied && (
                        <div className="space-y-1 mt-2">
                          <div className="flex gap-1">
                            <Button
                              size="sm"
                              variant="outline"
                              className="flex-1 text-xs font-elegant bg-orange-50 hover:bg-orange-100 border-orange-300 text-orange-700"
                              onClick={() => {
                                const tableOrder = orders.find(
                                  (o) =>
                                    o.type === "dine-in" &&
                                    o.tableNumber === table &&
                                    o.status !== "completed",
                                );
                                if (tableOrder) {
                                  prepareOrder(tableOrder);
                                }
                              }}
                            >
                              🍳 تجهيز
                            </Button>
                            <Button
                              size="sm"
                              variant="outline"
                              className="flex-1 text-xs font-elegant bg-blue-50 hover:bg-blue-100 border-blue-300 text-blue-700"
                              onClick={() => {
                                const tableOrder = orders.find(
                                  (o) =>
                                    o.type === "dine-in" &&
                                    o.tableNumber === table &&
                                    o.status !== "completed",
                                );
                                if (tableOrder) {
                                  // Mark order as completed and save to database
                                  const completedOrder = {
                                    ...tableOrder,
                                    status: "completed" as const,
                                  };
                                  saveToDatabase(completedOrder);

                                  // Update orders state to mark as completed
                                  setOrders((prevOrders) =>
                                    prevOrders.map((order) =>
                                      order.id === tableOrder.id
                                        ? {
                                            ...order,
                                            status: "completed" as const,
                                          }
                                        : order,
                                    ),
                                  );

                                  // Force clear everything related to this table
                                  if (currentOrder?.tableNumber === table) {
                                    setCurrentOrder(null);
                                  }
                                  if (selectedTable === table) {
                                    setSelectedTable(null);
                                  }

                                  // Clear table timing
                                  setTableStartTimes(prev => {
                                    const newTimes = { ...prev };
                                    delete newTimes[table];
                                    return newTimes;
                                  });
                                }
                              }}
                            >
                              💳 دفع
                            </Button>
                          </div>

                          <Button
                            size="sm"
                            variant="outline"
                            className="w-full text-xs font-elegant bg-purple-50 hover:bg-purple-100 border-purple-300 text-purple-700"
                            onClick={(e) => {
                              e.stopPropagation();
                              setEditingTableNumber(table);
                              setIsEditTableOpen(true);
                            }}
                          >
                            ✏️ تعديل الطاولة
                          </Button>
                        </div>
                      )}
                    </div>
                  );
                })}
              </div>
            </TabsContent>

            <TabsContent value="delivery" className="mt-4">
              <Card>
                <CardContent className="pt-4">
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="name">اسم العميل</Label>
                      <Input
                        id="name"
                        name="name"
                        value={customerInfo.name}
                        onChange={handleCustomerInfoChange}
                        placeholder="أدخل اسم العميل"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="phone">رقم الهاتف</Label>
                      <Input
                        id="phone"
                        name="phone"
                        value={customerInfo.phone}
                        onChange={handleCustomerInfoChange}
                        placeholder="أدخل رقم الهاتف"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="address">عنوان التوصيل</Label>
                      <Input
                        id="address"
                        name="address"
                        value={customerInfo.address}
                        onChange={handleCustomerInfoChange}
                        placeholder="أدخل عنوان التوصيل"
                      />
                    </div>
                    <Button
                      className="w-full"
                      onClick={createDeliveryOrder}
                      disabled={
                        !customerInfo.name ||
                        !customerInfo.phone ||
                        !customerInfo.address
                      }
                    >
                      إنشاء طلب توصيل
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="takeaway" className="mt-4">
              <Card>
                <CardContent className="pt-4">
                  <div className="space-y-4">
                    <p>إنشاء طلب سفري جديد</p>
                    <Button className="w-full" onClick={createTakeawayOrder}>
                      إنشاء طلب سفري
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>

          <div
            className="my-6 h-px"
            style={{ backgroundColor: '#dee2e6' }}
          ></div>

          <div>
            <div className="mb-4">
              <h4 className="text-md font-bold font-elegant mb-2" style={{ color: '#495057' }}>
                📋 الطلبات النشطة
              </h4>
              <p className="text-xs" style={{ color: '#6c757d' }}>
                الطلبات الحالية قيد التنفيذ
              </p>
            </div>
            <ScrollArea className="h-[300px]">
              <div className="space-y-2">
                {orders
                  .filter((order) => order.status !== "completed")
                  .map((order) => (
                    <Card
                      key={order.id}
                      className="cursor-pointer hover:bg-accent/50"
                    >
                      <CardContent className="p-3">
                        <div className="flex justify-between items-center">
                          <div>
                            <div className="font-medium">
                              {order.type === "dine-in"
                                ? `Table ${order.tableNumber}`
                                : order.type === "delivery"
                                  ? `Delivery: ${order.customerName}`
                                  : "Takeaway"}
                            </div>
                            <div className="text-sm text-muted-foreground">
                              {new Date(order.createdAt).toLocaleTimeString(
                                "ar-SA",
                              )}{" "}
                              • {order.items.length} عنصر
                            </div>
                          </div>
                          <div className="flex items-center gap-2">
                            {getStatusBadge(order.status)}
                            <ChevronRight className="h-4 w-4 text-muted-foreground" />
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
              </div>
            </ScrollArea>
          </div>
        </div>
        )}

        {/* Middle Panel - Order Details */}
        <div className={userType === "cashier" ? "w-1/3 border-r" : "w-1/2 border-r"}>
          {userType === "admin" ? (
            <div className="p-6 h-full" style={{ backgroundColor: '#f8f9fa' }}>
              <div className="text-center">
                <div className="mb-6">
                  <div
                    className="w-20 h-20 mx-auto rounded-full flex items-center justify-center mb-4"
                    style={{ backgroundColor: '#6c757d', color: 'white' }}
                  >
                    <span className="text-2xl font-bold">👨‍💼</span>
                  </div>
                  <h3 className="text-xl font-bold font-elegant" style={{ color: '#495057' }}>
                    لوحة تحكم المدير
                  </h3>
                  <p className="text-sm" style={{ color: '#6c757d' }}>
                    إدارة شاملة للمطعم
                  </p>
                </div>

                <div className="grid grid-cols-1 gap-4">
                  <div
                    className="p-4 rounded-lg border"
                    style={{ backgroundColor: 'white', borderColor: '#dee2e6' }}
                  >
                    <div className="text-2xl mb-2">📊</div>
                    <h4 className="font-semibold font-elegant" style={{ color: '#495057' }}>
                      تقارير المبيعات
                    </h4>
                    <p className="text-xs" style={{ color: '#6c757d' }}>
                      تحليل مفصل مع بحث بالتاريخ
                    </p>
                  </div>

                  <div
                    className="p-4 rounded-lg border"
                    style={{ backgroundColor: 'white', borderColor: '#dee2e6' }}
                  >
                    <div className="text-2xl mb-2">🍽️</div>
                    <h4 className="font-semibold font-elegant" style={{ color: '#495057' }}>
                      إدارة المخزون
                    </h4>
                    <p className="text-xs" style={{ color: '#6c757d' }}>
                      تعديل وإضافة المنتجات
                    </p>
                  </div>

                  <div
                    className="p-4 rounded-lg border"
                    style={{ backgroundColor: 'white', borderColor: '#dee2e6' }}
                  >
                    <div className="text-2xl mb-2">👁️</div>
                    <h4 className="font-semibold font-elegant" style={{ color: '#495057' }}>
                      مراقبة الطلبات
                    </h4>
                    <p className="text-xs" style={{ color: '#6c757d' }}>
                      عرض فقط - لا يمكن إتمام الطلبات
                    </p>
                  </div>
                </div>

                <div
                  className="mt-6 p-4 rounded-lg"
                  style={{ backgroundColor: '#e3f2fd', borderColor: '#2196f3' }}
                >
                  <div className="text-sm font-medium" style={{ color: '#1976d2' }}>
                    💡 نصيحة: استخدم تبويب "إدارة المخزون" لتعديل المنتجات
                  </div>
                </div>
              </div>
            </div>
          ) : currentOrder ? (
            <OrderDetails
              orderType={currentOrder.type}
              tableNumber={currentOrder.tableNumber}
              orderStatus={currentOrder.status}
              customerInfo={{
                name: currentOrder.customerName || "",
                phone: currentOrder.customerPhone || "",
                address: currentOrder.customerAddress || "",
              }}
              items={currentOrder.items}
              onUpdateStatus={(status) =>
                updateOrderStatus(currentOrder.id, status)
              }
              onProcessPayment={() => saveOrder()}
              onUpdateQuantity={(itemId, quantity) => {
                setCurrentOrder((prev) => {
                  if (!prev) return null;
                  const updatedItems = prev.items.map((item) =>
                    item.id === itemId ? { ...item, quantity } : item,
                  );
                  const total = updatedItems.reduce(
                    (sum, item) => sum + item.price * item.quantity,
                    0,
                  );
                  return { ...prev, items: updatedItems, total };
                });
              }}
              onRemoveItem={(itemId) => {
                setCurrentOrder((prev) => {
                  if (!prev) return null;
                  const updatedItems = prev.items.filter(
                    (item) => item.id !== itemId,
                  );
                  const total = updatedItems.reduce(
                    (sum, item) => sum + item.price * item.quantity,
                    0,
                  );
                  return { ...prev, items: updatedItems, total };
                });
              }}
              onPrintOrder={() => printOrder(currentOrder)}
              onPrepareOrder={() => prepareOrder(currentOrder)}
              language={language}
            />
          ) : (
            <div className="flex items-center justify-center h-full text-muted-foreground font-elegant">
              <div className="text-center">
                <div className="text-4xl mb-4">🍽️</div>
                <p>اختر طاولة أو أنشئ طلباً جديداً</p>
              </div>
            </div>
          )}
        </div>

        {/* Right Panel - Product Catalog */}
        <div className={userType === "cashier" ? "w-5/12" : "w-1/2"} style={{ display: 'flex', flexDirection: 'column', height: '100%' }}>
          {userType === "admin" ? (
            <div className="flex flex-col h-full" style={{ backgroundColor: '#f8f9fa' }}>
              <div className="p-6 flex-shrink-0">
                <h3 className="text-xl font-bold font-elegant mb-2" style={{ color: '#495057' }}>
                  📋 عرض المنتجات
                </h3>
                <p className="text-sm" style={{ color: '#6c757d' }}>
                  للتعديل، استخدم تبويب "إدارة المخزون"
                </p>
              </div>

              <div className="flex-1 overflow-hidden">
                <ProductCatalog
                  onAddToOrder={undefined}
                  showInventoryControls={false}
                  language={language}
                  adminViewMode={true}
                />
              </div>

              <div
                className="p-4 m-4 rounded-lg text-center flex-shrink-0"
                style={{ backgroundColor: '#fff3cd', borderColor: '#ffc107' }}
              >
                <div className="text-sm font-medium" style={{ color: '#856404' }}>
                  🔒 وضع العرض فقط - لا يمكن إضافة منتجات للطلبات
                </div>
                <div className="text-xs mt-1" style={{ color: '#856404' }}>
                  للتعديل: اذهب إلى تبويب "إدارة المخزون"
                </div>
              </div>
            </div>
          ) : (
            <div className="flex flex-col h-full">
              <ProductCatalog
                onAddToOrder={currentOrder ? addItemToOrder : undefined}
                showInventoryControls={false}
                language={language}
              />
            </div>
          )}
        </div>
      </div>

      {/* Order History Dialog */}
      <Dialog>
        <DialogTrigger asChild>
          <Button
            variant="outline"
            className="absolute bottom-4 right-4 rtl:right-auto rtl:left-4"
          >
            تاريخ الطلبات
          </Button>
        </DialogTrigger>
        <DialogContent className="max-w-4xl">
          <DialogHeader>
            <DialogTitle>تاريخ الطلبات</DialogTitle>
          </DialogHeader>
          <div className="mt-4">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>رقم الطلب</TableHead>
                  <TableHead>النوع</TableHead>
                  <TableHead>التفاصيل</TableHead>
                  <TableHead>العناصر</TableHead>
                  <TableHead>المجموع</TableHead>
                  <TableHead>الحالة</TableHead>
                  <TableHead>الإجراءات</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {orders.map((order) => (
                  <TableRow key={order.id}>
                    <TableCell className="font-medium">{order.id}</TableCell>
                    <TableCell className="capitalize">{order.type}</TableCell>
                    <TableCell>
                      {order.type === "dine-in"
                        ? `Table ${order.tableNumber}`
                        : order.type === "delivery"
                          ? `${order.customerName}, ${order.customerPhone}`
                          : "Takeaway"}
                    </TableCell>
                    <TableCell>{order.items.length}</TableCell>
                    <TableCell>{order.total.toFixed(2)} د.ع</TableCell>
                    <TableCell>{getStatusBadge(order.status)}</TableCell>
                    <TableCell>
                      <div className="flex gap-2">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => printOrder(order)}
                        >
                          <Printer className="h-4 w-4 mr-1 rtl:mr-0 rtl:ml-1" />
                          طباعة
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          className="bg-orange-50 hover:bg-orange-100"
                          onClick={() => prepareOrder(order)}
                          disabled={order.status === "completed"}
                        >
                          <Receipt className="h-4 w-4 mr-1 rtl:mr-0 rtl:ml-1" />
                          تجهيز
                        </Button>
                        <AlertDialog>
                          <AlertDialogTrigger asChild>
                            <Button size="sm" variant="outline">
                              <Clock className="h-4 w-4 mr-1 rtl:mr-0 rtl:ml-1" />
                              الحالة
                            </Button>
                          </AlertDialogTrigger>
                          <AlertDialogContent>
                            <AlertDialogHeader>
                              <AlertDialogTitle>
                                تحديث حالة الطلب
                              </AlertDialogTitle>
                              <AlertDialogDescription>
                                تغيير حالة الطلب {order.id}
                              </AlertDialogDescription>
                            </AlertDialogHeader>
                            <div className="flex flex-col gap-2 py-4">
                              <Button
                                variant={
                                  order.status === "new" ? "default" : "outline"
                                }
                                onClick={() =>
                                  updateOrderStatus(order.id, "new")
                                }
                              >
                                <AlertCircle className="h-4 w-4 mr-2 rtl:mr-0 rtl:ml-2" />
                                جديد
                              </Button>
                              <Button
                                variant={
                                  order.status === "in-progress"
                                    ? "default"
                                    : "outline"
                                }
                                onClick={() =>
                                  updateOrderStatus(order.id, "in-progress")
                                }
                              >
                                <Clock className="h-4 w-4 mr-2 rtl:mr-0 rtl:ml-2" />
                                قيد التنفيذ
                              </Button>
                              <Button
                                variant={
                                  order.status === "completed"
                                    ? "default"
                                    : "outline"
                                }
                                onClick={() =>
                                  updateOrderStatus(order.id, "completed")
                                }
                              >
                                <CheckCircle className="h-4 w-4 mr-2 rtl:mr-0 rtl:ml-2" />
                                مكتمل
                              </Button>
                            </div>
                            <AlertDialogFooter>
                              <AlertDialogCancel>إلغاء</AlertDialogCancel>
                            </AlertDialogFooter>
                          </AlertDialogContent>
                        </AlertDialog>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </DialogContent>
      </Dialog>

      {/* Edit Table Dialog */}
      <Dialog open={isEditTableOpen} onOpenChange={setIsEditTableOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle className="font-elegant">
              تعديل طاولة رقم {editingTableNumber}
            </DialogTitle>
          </DialogHeader>

          {editingTableNumber && (
            <div className="space-y-6">
              {/* Table Info */}
              <div
                className="p-4 rounded-lg"
                style={{ backgroundColor: '#f8f9fa', borderColor: '#dee2e6' }}
              >
                <div className="flex items-center justify-between mb-4">
                  <div>
                    <h4 className="font-bold font-elegant" style={{ color: '#495057' }}>
                      🪑 طاولة رقم {editingTableNumber}
                    </h4>
                    <p className="text-sm" style={{ color: '#6c757d' }}>
                      {getTableDuration(editingTableNumber) ?
                        `مفتوحة منذ ${getTableDuration(editingTableNumber)}` :
                        'طاولة جديدة'
                      }
                    </p>
                  </div>

                  <div className="text-right">
                    <div className="text-sm font-medium" style={{ color: '#495057' }}>
                      {new Date().toLocaleDateString('ar-SA')}
                    </div>
                    <div className="text-xs" style={{ color: '#6c757d' }}>
                      {new Date().toLocaleTimeString('ar-SA')}
                    </div>
                  </div>
                </div>
              </div>

              {/* Current Order Items */}
              {(() => {
                const tableOrder = orders.find(
                  (o) =>
                    o.type === "dine-in" &&
                    o.tableNumber === editingTableNumber &&
                    o.status !== "completed",
                );

                return tableOrder && tableOrder.items.length > 0 ? (
                  <div>
                    <h4 className="font-bold font-elegant mb-3" style={{ color: '#495057' }}>
                      🍽️ العناصر الحالية
                    </h4>
                    <div className="space-y-2 max-h-40 overflow-y-auto">
                      {tableOrder.items.map((item, index) => (
                        <div
                          key={index}
                          className="flex items-center justify-between p-3 rounded-lg"
                          style={{ backgroundColor: 'white', borderColor: '#dee2e6' }}
                        >
                          <div className="flex-1">
                            <div className="font-medium">{item.name}</div>
                            <div className="text-sm" style={{ color: '#6c757d' }}>
                              {item.price.toFixed(2)} ر.س × {item.quantity}
                            </div>
                          </div>

                          <div className="flex items-center gap-2">
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => {
                                // Decrease quantity
                                if (item.quantity > 1) {
                                  const updatedItems = tableOrder.items.map(i =>
                                    i.id === item.id ? { ...i, quantity: i.quantity - 1 } : i
                                  );
                                  const total = updatedItems.reduce((sum, i) => sum + i.price * i.quantity, 0);
                                  const updatedOrder = { ...tableOrder, items: updatedItems, total };

                                  setOrders(prev => prev.map(o => o.id === tableOrder.id ? updatedOrder : o));
                                  if (currentOrder?.id === tableOrder.id) {
                                    setCurrentOrder(updatedOrder);
                                  }
                                }
                              }}
                            >
                              -
                            </Button>

                            <span className="w-8 text-center font-medium">
                              {item.quantity}
                            </span>

                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => {
                                // Increase quantity
                                const updatedItems = tableOrder.items.map(i =>
                                  i.id === item.id ? { ...i, quantity: i.quantity + 1 } : i
                                );
                                const total = updatedItems.reduce((sum, i) => sum + i.price * i.quantity, 0);
                                const updatedOrder = { ...tableOrder, items: updatedItems, total };

                                setOrders(prev => prev.map(o => o.id === tableOrder.id ? updatedOrder : o));
                                if (currentOrder?.id === tableOrder.id) {
                                  setCurrentOrder(updatedOrder);
                                }
                              }}
                            >
                              +
                            </Button>

                            <Button
                              size="sm"
                              variant="destructive"
                              onClick={() => {
                                // Remove item
                                const updatedItems = tableOrder.items.filter(i => i.id !== item.id);
                                const total = updatedItems.reduce((sum, i) => sum + i.price * i.quantity, 0);
                                const updatedOrder = { ...tableOrder, items: updatedItems, total };

                                setOrders(prev => prev.map(o => o.id === tableOrder.id ? updatedOrder : o));
                                if (currentOrder?.id === tableOrder.id) {
                                  setCurrentOrder(updatedOrder);
                                }
                              }}
                            >
                              🗑️
                            </Button>
                          </div>
                        </div>
                      ))}
                    </div>

                    <div
                      className="mt-4 p-3 rounded-lg text-center"
                      style={{ backgroundColor: '#e8f5e8', borderColor: '#28a745' }}
                    >
                      <div className="font-bold" style={{ color: '#155724' }}>
                        المجموع: {tableOrder.total.toFixed(2)} ر.س
                      </div>
                    </div>
                  </div>
                ) : (
                  <div
                    className="p-6 text-center rounded-lg"
                    style={{ backgroundColor: '#fff3cd', borderColor: '#ffc107' }}
                  >
                    <div className="text-4xl mb-2">🍽️</div>
                    <div className="font-medium" style={{ color: '#856404' }}>
                      لا توجد عناصر في هذه الطاولة
                    </div>
                    <div className="text-sm mt-1" style={{ color: '#856404' }}>
                      أضف منتجات من قائمة المنتجات
                    </div>
                  </div>
                );
              })()}

              {/* Action Buttons */}
              <div className="flex gap-3">
                <Button
                  onClick={() => setIsEditTableOpen(false)}
                  className="flex-1"
                  style={{
                    backgroundColor: '#28a745',
                    borderColor: '#28a745',
                    color: 'white'
                  }}
                >
                  ✅ حفظ التغييرات
                </Button>

                <Button
                  variant="outline"
                  onClick={() => setIsEditTableOpen(false)}
                  className="flex-1"
                >
                  ❌ إلغاء
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default OrderManagement;
