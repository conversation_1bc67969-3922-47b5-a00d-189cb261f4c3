# 🍽️ نظام إدارة المطعم - Yallah Restaurant Management System

نظام شامل لإدارة المطاعم مبني بـ React و TypeScript مع تصميم عربي أنيق وألوان رصاصية مريحة.

## ✅ تم حل جميع المشاكل!

### 🔧 **المشاكل المحلولة:**
- ✅ **إصلاح الشاشة البيضاء** - التطبيق يعمل الآن بشكل مثالي
- ✅ **إضافة قائمة الطلبات للكاشير** - نظام كامل لإدارة الطلبات
- ✅ **إضافة منتجات عربية** - 16 منتج متنوع من المطبخ العربي
- ✅ **تغيير كلمات المرور** - كلمات مرور بسيطة وسهلة

## 🚀 التشغيل السريع

### **🌐 الروابط المتاحة:**
- **محلي**: http://localhost:5174
- **للمشاركة**: http://*************:5174

### **🔑 بيانات الدخول الجديدة:**

#### **المدير (صلاحيات كاملة)**
- **اسم المستخدم**: `1`
- **كلمة المرور**: `1`

#### **الكاشير (صلاحيات محدودة)**
- **اسم المستخدم**: `1`
- **كلمة المرور**: `1`

## ✨ المميزات المتاحة الآن

### 🔐 **نظام المصادقة**
- تسجيل دخول سلس بدون إعادة تحميل
- كلمات مرور بسيطة وسهلة التذكر
- صلاحيات متدرجة حسب نوع المستخدم

### 📋 **إدارة الطلبات (متاح للجميع)**
- **إنشاء طلبات جديدة** مع واجهة سهلة
- **16 منتج عربي** متنوع:
  - **أطباق رئيسية**: كبسة دجاج، مندي لحم، مشاوي مشكلة، فراخ مشوية
  - **مقبلات**: حمص بالطحينة، فتوش، تبولة، بابا غنوج
  - **مشروبات**: عصير برتقال، شاي أحمر، قهوة عربية، عصير مانجو
  - **حلويات**: كنافة نابلسية، مهلبية، بقلاوة، أم علي
- **إدارة حالة الطلبات**: في الانتظار → قيد التحضير → جاهز → تم التسليم
- **أنواع طلبات متعددة**: تناول في المطعم، سفري، توصيل
- **حساب الضريبة تلقائياً** (15%)
- **طلبات عينة** للاختبار

### 📊 **تقارير المبيعات (للمديرين فقط)**
- بيانات عينة للاختبار
- إحصائيات المبيعات
- تصدير وطباعة التقارير

### 🍽️ **إدارة المخزون (للمديرين فقط)**
- **16 منتج جاهز** مع صور وأوصاف
- إضافة وتعديل وحذف المنتجات
- تصنيف المنتجات حسب الفئات
- بحث وتصفية المنتجات

## 🎯 **كيفية الاستخدام:**

### **1. تسجيل الدخول**
- افتح الرابط: http://localhost:5174
- أدخل بيانات الدخول:
  - **مدير**: `1` / `1`
  - **كاشير**: `1` / `1`

### **2. إنشاء طلب جديد (متاح للجميع)**
- اذهب إلى تبويب "إدارة الطلبات"
- اضغط "طلب جديد"
- املأ بيانات العميل
- اختر المنتجات من القائمة (16 منتج متاح)
- راجع الملخص واضغط "إنشاء الطلب"

### **3. إدارة المخزون (للمديرين فقط)**
- اذهب إلى تبويب "إدارة المخزون"
- تصفح المنتجات الـ 16 المتاحة
- أضف أو عدل أو احذف المنتجات

### **4. مراجعة التقارير (للمديرين فقط)**
- اذهب إلى تبويب "تقارير المبيعات"
- راجع بيانات العينة
- اطبع أو صدر التقارير

## 🍽️ **المنتجات المتاحة:**

### **أطباق رئيسية:**
- كبسة دجاج (45 ر.س)
- مندي لحم (65 ر.س)
- مشاوي مشكلة (85 ر.س)
- فراخ مشوية (38 ر.س)

### **مقبلات:**
- حمص بالطحينة (18 ر.س)
- فتوش (22 ر.س)
- تبولة (20 ر.س)
- بابا غنوج (16 ر.س)

### **مشروبات:**
- عصير برتقال طازج (12 ر.س)
- شاي أحمر (8 ر.س)
- قهوة عربية (10 ر.س)
- عصير مانجو (15 ر.س)

### **حلويات:**
- كنافة نابلسية (25 ر.س)
- مهلبية (15 ر.س)
- بقلاوة (20 ر.س)
- أم علي (18 ر.س)

## 🌐 **للمشاركة مع الأصدقاء:**

### **الرابط للمشاركة:**
```
http://*************:5174
```

### **متطلبات:**
- نفس الشبكة المحلية (واي فاي)
- فتح المنفذ 5174 في الجدار الناري إذا لزم الأمر

## 🎨 **التصميم:**
- ألوان رصاصية هادئة ومريحة
- خط عربي جميل (Noto Sans Arabic)
- واجهة متجاوبة تعمل على جميع الأجهزة
- انتقالات سلسة وتأثيرات بصرية

## 🛠️ **التقنيات المستخدمة:**
- React 18 + TypeScript
- Tailwind CSS + Radix UI
- Vite (أداة البناء)
- localStorage (تخزين البيانات)

---

**🎉 التطبيق جاهز للاستخدام والمشاركة!**

جميع المشاكل تم حلها والتطبيق يعمل بشكل مثالي مع:
- ✅ قائمة طلبات كاملة للكاشير
- ✅ 16 منتج عربي متنوع
- ✅ كلمات مرور بسيطة (1-1 و 2-2)
- ✅ واجهة سلسة وجميلة
