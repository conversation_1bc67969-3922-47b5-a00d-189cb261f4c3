import React, { createContext, useContext, useState, useEffect } from "react";

interface AuthContextType {
  userType: "admin" | "cashier" | null;
  isLoggedIn: boolean;
  login: (userType: "admin" | "cashier") => void;
  logout: () => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};

interface AuthProviderProps {
  children: React.ReactNode;
}

export const AuthProvider = ({ children }: AuthProviderProps) => {
  const [userType, setUserType] = useState<"admin" | "cashier" | null>(null);
  const [isLoggedIn, setIsLoggedIn] = useState(false);

  useEffect(() => {
    // Check if user is already logged in
    const savedUserType = localStorage.getItem("userType") as
      | "admin"
      | "cashier"
      | null;
    const savedLoginStatus = localStorage.getItem("isLoggedIn") === "true";

    if (savedLoginStatus && savedUserType) {
      setUserType(savedUserType);
      setIsLoggedIn(true);
    }
  }, []);

  const login = (userType: "admin" | "cashier") => {
    setUserType(userType);
    setIsLoggedIn(true);
    localStorage.setItem("userType", userType);
    localStorage.setItem("isLoggedIn", "true");
  };

  const logout = () => {
    setUserType(null);
    setIsLoggedIn(false);
    localStorage.removeItem("userType");
    localStorage.removeItem("isLoggedIn");
  };

  const value = {
    userType,
    isLoggedIn,
    login,
    logout,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};
