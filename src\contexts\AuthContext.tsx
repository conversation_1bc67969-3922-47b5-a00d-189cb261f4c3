import React, { createContext, useContext, useState, useEffect } from "react";
import { db, User } from "@/lib/database";

interface AuthContextType {
  user: User | null;
  isLoggedIn: boolean;
  login: (username: string, password: string) => Promise<boolean>;
  logout: () => void;
  hasPermission: (permission: string) => boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};

interface AuthProviderProps {
  children: React.ReactNode;
}

export const AuthProvider = ({ children }: AuthProviderProps) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoggedIn, setIsLoggedIn] = useState(false);

  useEffect(() => {
    // Check if user is already logged in
    const savedUserId = localStorage.getItem("currentUserId");
    if (savedUserId) {
      const users = db.getUsers();
      const savedUser = users.find(u => u.id === savedUserId);
      if (savedUser && savedUser.active) {
        setUser(savedUser);
        setIsLoggedIn(true);
      } else {
        // Clear invalid session
        localStorage.removeItem("currentUserId");
      }
    }
  }, []);

  const login = async (username: string, password: string): Promise<boolean> => {
    try {
      const foundUser = db.findUserByUsername(username);

      if (foundUser && foundUser.password === password && foundUser.active) {
        setUser(foundUser);
        setIsLoggedIn(true);
        localStorage.setItem("currentUserId", foundUser.id);

        // Update last login time
        db.updateUserLastLogin(foundUser.id);

        return true;
      }

      return false;
    } catch (error) {
      console.error("Login error:", error);
      return false;
    }
  };

  const logout = () => {
    setUser(null);
    setIsLoggedIn(false);
    localStorage.removeItem("currentUserId");
  };

  const hasPermission = (permission: string): boolean => {
    if (!user) return false;
    if (user.permissions.includes("all")) return true;
    return user.permissions.includes(permission);
  };

  const value = {
    user,
    isLoggedIn,
    login,
    logout,
    hasPermission,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};
