import React, { useState } from "react";
import { Search, Plus, Edit, Trash2 } from "lucide-react";
import { But<PERSON> } from "./ui/button";
import { Card, CardContent } from "./ui/card";
import { Input } from "./ui/input";
import { Tabs, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "./ui/tabs";
import { Badge } from "./ui/badge";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "./ui/dialog";
import { Label } from "./ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "./ui/select";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "./ui/alert-dialog";

interface Product {
  id: string;
  name: string;
  price: number;
  image: string;
  category: string;
  description?: string;
}

interface ProductCatalogProps {
  onAddToOrder?: (product: Product) => void;
  showInventoryControls?: boolean;
  language?: "english" | "arabic";
}

const ProductCatalog: React.FC<ProductCatalogProps> = ({
  onAddToOrder = () => {},
  showInventoryControls = false,
  language = "arabic",
}) => {
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("all");
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [editingProduct, setEditingProduct] = useState<Product | null>(null);
  const [newProduct, setNewProduct] = useState({
    name: "",
    price: 0,
    category: "",
    description: "",
    image: "",
  });

  // Mock product data with Arabic names
  const [products, setProducts] = useState<Product[]>([
    // أطباق رئيسية
    {
      id: "1",
      name: "كبسة دجاج",
      price: 45.00,
      image: "https://images.unsplash.com/photo-1563379091339-03246963d96c?w=300&q=80",
      category: "أطباق رئيسية",
      description: "أرز بسمتي مع دجاج مشوي وخلطة البهارات الخاصة"
    },
    {
      id: "2",
      name: "مندي لحم",
      price: 65.00,
      image: "https://images.unsplash.com/photo-1574484284002-952d92456975?w=300&q=80",
      category: "أطباق رئيسية",
      description: "لحم خروف طري مع أرز مندي مدخن"
    },
    {
      id: "3",
      name: "مشاوي مشكلة",
      price: 85.00,
      image: "https://images.unsplash.com/photo-1544025162-d76694265947?w=300&q=80",
      category: "أطباق رئيسية",
      description: "تشكيلة من اللحوم المشوية مع الخضار"
    },
    {
      id: "4",
      name: "فراخ مشوية",
      price: 38.00,
      image: "https://images.unsplash.com/photo-1598103442097-8b74394b95c6?w=300&q=80",
      category: "أطباق رئيسية",
      description: "دجاج كامل مشوي مع البهارات والأعشاب"
    },

    // مقبلات
    {
      id: "5",
      name: "حمص بالطحينة",
      price: 18.00,
      image: "https://images.unsplash.com/photo-1571197119282-7c4d9e3e8b8e?w=300&q=80",
      category: "مقبلات",
      description: "حمص كريمي مع الطحينة وزيت الزيتون"
    },
    {
      id: "6",
      name: "فتوش",
      price: 22.00,
      image: "https://images.unsplash.com/photo-1512621776951-a57141f2eefd?w=300&q=80",
      category: "مقبلات",
      description: "سلطة خضار مشكلة مع الخبز المحمص"
    },
    {
      id: "7",
      name: "تبولة",
      price: 20.00,
      image: "https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=300&q=80",
      category: "مقبلات",
      description: "سلطة البقدونس مع الطماطم والبرغل"
    },
    {
      id: "8",
      name: "بابا غنوج",
      price: 16.00,
      image: "https://images.unsplash.com/photo-1571197119282-7c4d9e3e8b8e?w=300&q=80",
      category: "مقبلات",
      description: "باذنجان مشوي مع الطحينة والثوم"
    },

    // مشروبات
    {
      id: "9",
      name: "عصير برتقال طازج",
      price: 12.00,
      image: "https://images.unsplash.com/photo-1621506289937-a8e4df240d0b?w=300&q=80",
      category: "مشروبات",
      description: "عصير برتقال طبيعي 100%"
    },
    {
      id: "10",
      name: "شاي أحمر",
      price: 8.00,
      image: "https://images.unsplash.com/photo-1544787219-7f47ccb76574?w=300&q=80",
      category: "مشروبات",
      description: "شاي أحمر تقليدي مع السكر"
    },
    {
      id: "11",
      name: "قهوة عربية",
      price: 10.00,
      image: "https://images.unsplash.com/photo-1559056199-641a0ac8b55e?w=300&q=80",
      category: "مشروبات",
      description: "قهوة عربية أصيلة مع الهيل"
    },
    {
      id: "12",
      name: "عصير مانجو",
      price: 15.00,
      image: "https://images.unsplash.com/photo-1546173159-315724a31696?w=300&q=80",
      category: "مشروبات",
      description: "عصير مانجو طازج ومنعش"
    },

    // حلويات
    {
      id: "13",
      name: "كنافة نابلسية",
      price: 25.00,
      image: "https://images.unsplash.com/photo-1578985545062-69928b1d9587?w=300&q=80",
      category: "حلويات",
      description: "كنافة محشوة بالجبن مع القطر"
    },
    {
      id: "14",
      name: "مهلبية",
      price: 15.00,
      image: "https://images.unsplash.com/photo-1551024506-0bccd828d307?w=300&q=80",
      category: "حلويات",
      description: "حلى كريمي بالحليب مع ماء الورد"
    },
    {
      id: "15",
      name: "بقلاوة",
      price: 20.00,
      image: "https://images.unsplash.com/photo-1571115764595-644a1f56a55c?w=300&q=80",
      category: "حلويات",
      description: "حلوى شرقية بالعجين الرقيق والمكسرات"
    },
    {
      id: "16",
      name: "أم علي",
      price: 18.00,
      image: "https://images.unsplash.com/photo-1551024506-0bccd828d307?w=300&q=80",
      category: "حلويات",
      description: "حلى ساخن بالحليب والمكسرات والزبيب"
    }
  ]);

  const handleAddProduct = () => {
    if (!newProduct.name || !newProduct.price || !newProduct.category) return;

    const product: Product = {
      id: `product-${Date.now()}`,
      name: newProduct.name,
      price: newProduct.price,
      category: newProduct.category,
      description: newProduct.description,
      image:
        newProduct.image ||
        "https://images.unsplash.com/photo-1546069901-ba9599a7e63c?w=300&q=80",
    };

    setProducts([...products, product]);
    setNewProduct({
      name: "",
      price: 0,
      category: "",
      description: "",
      image: "",
    });
    setIsAddDialogOpen(false);
  };

  const handleEditProduct = () => {
    if (!editingProduct) return;

    setProducts(
      products.map((p) => (p.id === editingProduct.id ? editingProduct : p)),
    );
    setEditingProduct(null);
    setIsEditDialogOpen(false);
  };

  const handleDeleteProduct = (productId: string) => {
    setProducts(products.filter((p) => p.id !== productId));
  };

  // Kitchen printer function
  const handleKitchenPrint = (product: Product) => {
    const printContent = `
=================================
        طلب المطبخ
=================================
المنتج: ${product.name}
الوصف: ${product.description}
السعر: ${product.price.toFixed(2)} ر.س
الوقت: ${new Date().toLocaleString('ar-SA')}
=================================
    `;

    // Create a new window for printing
    const printWindow = window.open('', '_blank');
    if (printWindow) {
      printWindow.document.write(`
        <html>
          <head>
            <title>طلب المطبخ</title>
            <style>
              body {
                font-family: 'Courier New', monospace;
                direction: rtl;
                text-align: center;
                margin: 20px;
                font-size: 14px;
              }
              pre {
                white-space: pre-wrap;
                font-family: inherit;
              }
            </style>
          </head>
          <body>
            <pre>${printContent}</pre>
          </body>
        </html>
      `);
      printWindow.document.close();
      printWindow.print();
      printWindow.close();
    }

    // Show confirmation
    alert(`تم إرسال طلب "${product.name}" إلى طابعة المطبخ! 🍳`);
  };

  // Extract unique categories
  const categories = [
    language === "english" ? "all" : "الكل",
    ...new Set(products.map((product) => product.category)),
  ];

  const getCategoryDisplayName = (category: string) => {
    if (category === "all" || category === "الكل")
      return language === "english" ? "All" : "الكل";
    return category;
  };

  // Filter products based on search query and selected category
  const filteredProducts = products.filter((product) => {
    const matchesSearch = product.name
      .toLowerCase()
      .includes(searchQuery.toLowerCase());
    const matchesCategory =
      selectedCategory === "all" ||
      selectedCategory === "الكل" ||
      product.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  return (
    <div className="flex flex-col h-full bg-white rounded-md shadow-sm">
      <div className="p-4 border-b">
        <div className="flex items-center gap-2 mb-4">
          <div className="relative flex-1">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="البحث عن المنتجات..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-8"
            />
          </div>
          {showInventoryControls && (
            <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
              <DialogTrigger asChild>
                <Button className="flex items-center gap-2">
                  <Plus className="h-4 w-4" />
                  إضافة منتج جديد
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>إضافة منتج جديد</DialogTitle>
                </DialogHeader>
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="product-name">اسم المنتج</Label>
                    <Input
                      id="product-name"
                      value={newProduct.name}
                      onChange={(e) =>
                        setNewProduct({ ...newProduct, name: e.target.value })
                      }
                      placeholder="أدخل اسم المنتج"
                    />
                  </div>
                  <div>
                    <Label htmlFor="product-price">السعر (ر.س)</Label>
                    <Input
                      id="product-price"
                      type="number"
                      value={newProduct.price}
                      onChange={(e) =>
                        setNewProduct({
                          ...newProduct,
                          price: parseFloat(e.target.value) || 0,
                        })
                      }
                      placeholder="أدخل السعر"
                    />
                  </div>
                  <div>
                    <Label htmlFor="product-category">الفئة</Label>
                    <Select
                      value={newProduct.category}
                      onValueChange={(value) =>
                        setNewProduct({ ...newProduct, category: value })
                      }
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="اختر الفئة" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="برجر">برجر</SelectItem>
                        <SelectItem value="مقبلات">مقبلات</SelectItem>
                        <SelectItem value="سلطات">سلطات</SelectItem>
                        <SelectItem value="مشروبات">مشروبات</SelectItem>
                        <SelectItem value="بيتزا">بيتزا</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="product-description">الوصف</Label>
                    <Input
                      id="product-description"
                      value={newProduct.description}
                      onChange={(e) =>
                        setNewProduct({
                          ...newProduct,
                          description: e.target.value,
                        })
                      }
                      placeholder="أدخل وصف المنتج"
                    />
                  </div>
                  <div>
                    <Label htmlFor="product-image">رابط الصورة</Label>
                    <Input
                      id="product-image"
                      value={newProduct.image}
                      onChange={(e) =>
                        setNewProduct({ ...newProduct, image: e.target.value })
                      }
                      placeholder="أدخل رابط الصورة (اختياري)"
                    />
                  </div>
                  <div className="flex gap-2">
                    <Button onClick={handleAddProduct} className="flex-1">
                      إضافة المنتج
                    </Button>
                    <Button
                      variant="outline"
                      onClick={() => setIsAddDialogOpen(false)}
                      className="flex-1"
                    >
                      إلغاء
                    </Button>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          )}
        </div>

        <Tabs
          defaultValue={language === "english" ? "all" : "الكل"}
          value={selectedCategory}
          onValueChange={setSelectedCategory}
        >
          <TabsList className="w-full overflow-x-auto flex-wrap">
            {categories.map((category) => (
              <TabsTrigger
                key={category}
                value={category}
                className="capitalize"
              >
                {getCategoryDisplayName(category)}
              </TabsTrigger>
            ))}
          </TabsList>
        </Tabs>
      </div>

      <div className="flex-1 overflow-y-auto p-4">
        <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
          {filteredProducts.map((product) => (
            <Card key={product.id} className="overflow-hidden">
              <div className="relative h-32 overflow-hidden">
                <img
                  src={product.image}
                  alt={product.name}
                  className="w-full h-full object-cover"
                />
                <Badge className="absolute top-2 right-2">
                  {product.price.toFixed(2)} ر.س
                </Badge>
              </div>
              <CardContent className="p-3">
                <h3 className="font-medium text-sm mb-1 truncate">
                  {product.name}
                </h3>
                <p className="text-xs text-muted-foreground line-clamp-2 h-8">
                  {product.description || `${product.category} item`}
                </p>
                <div className="mt-2 flex justify-between items-center">
                  {showInventoryControls ? (
                    <div className="flex gap-1 w-full">
                      <Button
                        size="sm"
                        variant="outline"
                        className="flex-1"
                        onClick={() => {
                          setEditingProduct(product);
                          setIsEditDialogOpen(true);
                        }}
                      >
                        <Edit className="h-3 w-3 mr-1" />
                        تعديل
                      </Button>
                      <AlertDialog>
                        <AlertDialogTrigger asChild>
                          <Button
                            size="sm"
                            variant="destructive"
                            className="flex-1"
                          >
                            <Trash2 className="h-3 w-3 mr-1" />
                            حذف
                          </Button>
                        </AlertDialogTrigger>
                        <AlertDialogContent>
                          <AlertDialogHeader>
                            <AlertDialogTitle>تأكيد الحذف</AlertDialogTitle>
                            <AlertDialogDescription>
                              هل أنت متأكد من حذف {product.name}؟ لا يمكن
                              التراجع عن هذا الإجراء.
                            </AlertDialogDescription>
                          </AlertDialogHeader>
                          <AlertDialogFooter>
                            <AlertDialogCancel>إلغاء</AlertDialogCancel>
                            <AlertDialogAction
                              onClick={() => handleDeleteProduct(product.id)}
                            >
                              حذف
                            </AlertDialogAction>
                          </AlertDialogFooter>
                        </AlertDialogContent>
                      </AlertDialog>
                    </div>
                  ) : onAddToOrder ? (
                    <div className="flex gap-1 w-full">
                      <Button
                        onClick={() => onAddToOrder(product)}
                        size="sm"
                        className="flex-1"
                      >
                        إضافة للطلب
                      </Button>
                      <Button
                        onClick={() => {
                          // Add to order first
                          onAddToOrder(product);
                          // Then send to kitchen printer immediately
                          handleKitchenPrint(product);
                        }}
                        size="sm"
                        variant="outline"
                        className="px-2"
                        style={{
                          backgroundColor: '#28a745',
                          borderColor: '#28a745',
                          color: 'white'
                        }}
                        title="إضافة وتجهيز فوري"
                      >
                        🍳
                      </Button>
                    </div>
                  ) : (
                    <div className="text-center text-xs text-muted-foreground">
                      عرض فقط
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {filteredProducts.length === 0 && (
          <div className="flex flex-col items-center justify-center h-40 text-muted-foreground">
            <p>لم يتم العثور على منتجات</p>
            <Button
              variant="link"
              onClick={() => {
                setSearchQuery("");
                setSelectedCategory("الكل");
              }}
            >
              مسح المرشحات
            </Button>
          </div>
        )}
      </div>

      {/* Edit Product Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>تعديل المنتج</DialogTitle>
          </DialogHeader>
          {editingProduct && (
            <div className="space-y-4">
              <div>
                <Label htmlFor="edit-product-name">اسم المنتج</Label>
                <Input
                  id="edit-product-name"
                  value={editingProduct.name}
                  onChange={(e) =>
                    setEditingProduct({
                      ...editingProduct,
                      name: e.target.value,
                    })
                  }
                  placeholder="أدخل اسم المنتج"
                />
              </div>
              <div>
                <Label htmlFor="edit-product-price">السعر (ر.س)</Label>
                <Input
                  id="edit-product-price"
                  type="number"
                  value={editingProduct.price}
                  onChange={(e) =>
                    setEditingProduct({
                      ...editingProduct,
                      price: parseFloat(e.target.value) || 0,
                    })
                  }
                  placeholder="أدخل السعر"
                />
              </div>
              <div>
                <Label htmlFor="edit-product-category">الفئة</Label>
                <Select
                  value={editingProduct.category}
                  onValueChange={(value) =>
                    setEditingProduct({ ...editingProduct, category: value })
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="اختر الفئة" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="برجر">برجر</SelectItem>
                    <SelectItem value="مقبلات">مقبلات</SelectItem>
                    <SelectItem value="سلطات">سلطات</SelectItem>
                    <SelectItem value="مشروبات">مشروبات</SelectItem>
                    <SelectItem value="بيتزا">بيتزا</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="edit-product-description">الوصف</Label>
                <Input
                  id="edit-product-description"
                  value={editingProduct.description || ""}
                  onChange={(e) =>
                    setEditingProduct({
                      ...editingProduct,
                      description: e.target.value,
                    })
                  }
                  placeholder="أدخل وصف المنتج"
                />
              </div>
              <div>
                <Label htmlFor="edit-product-image">رابط الصورة</Label>
                <Input
                  id="edit-product-image"
                  value={editingProduct.image}
                  onChange={(e) =>
                    setEditingProduct({
                      ...editingProduct,
                      image: e.target.value,
                    })
                  }
                  placeholder="أدخل رابط الصورة"
                />
              </div>
              <div className="flex gap-2">
                <Button onClick={handleEditProduct} className="flex-1">
                  حفظ التغييرات
                </Button>
                <Button
                  variant="outline"
                  onClick={() => setIsEditDialogOpen(false)}
                  className="flex-1"
                >
                  إلغاء
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default ProductCatalog;
