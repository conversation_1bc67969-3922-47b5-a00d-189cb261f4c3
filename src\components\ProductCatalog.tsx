import React, { useState } from "react";
import { Search, Plus, Edit, Trash2 } from "lucide-react";
import { But<PERSON> } from "./ui/button";
import { Card, CardContent } from "./ui/card";
import { Input } from "./ui/input";
import { Tabs, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "./ui/tabs";
import { Badge } from "./ui/badge";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "./ui/dialog";
import { Label } from "./ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "./ui/select";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "./ui/alert-dialog";

interface Product {
  id: string;
  name: string;
  price: number;
  image: string;
  category: string;
  description?: string;
}

interface ProductCatalogProps {
  onAddToOrder?: (product: Product) => void;
  showInventoryControls?: boolean;
  language?: "english" | "arabic";
}

const ProductCatalog: React.FC<ProductCatalogProps> = ({
  onAddToOrder = () => {},
  showInventoryControls = false,
  language = "arabic",
}) => {
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("all");
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [editingProduct, setEditingProduct] = useState<Product | null>(null);
  const [newProduct, setNewProduct] = useState({
    name: "",
    price: 0,
    category: "",
    description: "",
    image: "",
  });

  // Mock product data with Arabic names
  const [products, setProducts] = useState<Product[]>([
    {
      id: "1",
      name: language === "english" ? "Chicken Burger" : "برجر دجاج",
      price: 25.99,
      image:
        "https://images.unsplash.com/photo-1568901346375-23c9450c58cd?w=300&q=80",
      category: language === "english" ? "burgers" : "برجر",
      description:
        language === "english"
          ? "Juicy chicken patty with lettuce, tomato, and special sauce"
          : "قطعة دجاج طرية مع الخس والطماطم والصوص الخاص",
    },
    {
      id: "2",
      name: language === "english" ? "Beef Burger" : "برجر لحم",
      price: 29.99,
      image:
        "https://images.unsplash.com/photo-1571091718767-18b5b1457add?w=300&q=80",
      category: language === "english" ? "burgers" : "برجر",
      description:
        language === "english"
          ? "Premium beef patty with cheese and fresh vegetables"
          : "قطعة لحم فاخرة مع الجبن والخضار الطازجة",
    },
    {
      id: "3",
      name: language === "english" ? "French Fries" : "بطاطس مقلية",
      price: 12.99,
      image:
        "https://images.unsplash.com/photo-1630384060421-cb20d0e0649d?w=300&q=80",
      category: language === "english" ? "sides" : "مقبلات",
      description:
        language === "english"
          ? "Crispy golden fries with sea salt"
          : "بطاطس مقرمشة ذهبية مع ملح البحر",
    },
    {
      id: "4",
      name: language === "english" ? "Caesar Salad" : "سلطة سيزر",
      price: 18.99,
      image:
        "https://images.unsplash.com/photo-1550304943-4f24f54ddde9?w=300&q=80",
      category: language === "english" ? "salads" : "سلطات",
      description:
        language === "english"
          ? "Fresh romaine lettuce with Caesar dressing and croutons"
          : "خس روماني طازج مع صوص سيزر والخبز المحمص",
    },
    {
      id: "5",
      name: language === "english" ? "Coca Cola" : "كوكا كولا",
      price: 5.99,
      image:
        "https://images.unsplash.com/photo-1554866585-cd94860890b7?w=300&q=80",
      category: language === "english" ? "drinks" : "مشروبات",
      description:
        language === "english"
          ? "Classic cola drink, served cold"
          : "مشروب كولا كلاسيكي، يُقدم بارداً",
    },
    {
      id: "6",
      name:
        language === "english" ? "Chocolate Milkshake" : "ميلك شيك شوكولاتة",
      price: 15.99,
      image:
        "https://images.unsplash.com/photo-1563805042-7684c019e1cb?w=300&q=80",
      category: language === "english" ? "drinks" : "مشروبات",
      description:
        language === "english"
          ? "Rich chocolate milkshake with whipped cream"
          : "ميلك شيك شوكولاتة غني مع الكريمة المخفوقة",
    },
    {
      id: "7",
      name: language === "english" ? "Chicken Wings" : "أجنحة دجاج",
      price: 32.99,
      image:
        "https://images.unsplash.com/photo-1608039755401-742074f0548d?w=300&q=80",
      category: language === "english" ? "appetizers" : "مقبلات",
      description:
        language === "english"
          ? "Spicy buffalo wings with blue cheese dip"
          : "أجنحة دجاج حارة مع صوص الجبن الأزرق",
    },
    {
      id: "8",
      name: language === "english" ? "Margherita Pizza" : "بيتزا مارجريتا",
      price: 38.99,
      image:
        "https://images.unsplash.com/photo-1604917877934-07d8d248d396?w=300&q=80",
      category: language === "english" ? "pizza" : "بيتزا",
      description:
        language === "english"
          ? "Classic pizza with tomato sauce, mozzarella, and basil"
          : "بيتزا كلاسيكية مع صوص الطماطم والموزاريلا والريحان",
    },
  ]);

  const handleAddProduct = () => {
    if (!newProduct.name || !newProduct.price || !newProduct.category) return;

    const product: Product = {
      id: `product-${Date.now()}`,
      name: newProduct.name,
      price: newProduct.price,
      category: newProduct.category,
      description: newProduct.description,
      image:
        newProduct.image ||
        "https://images.unsplash.com/photo-1546069901-ba9599a7e63c?w=300&q=80",
    };

    setProducts([...products, product]);
    setNewProduct({
      name: "",
      price: 0,
      category: "",
      description: "",
      image: "",
    });
    setIsAddDialogOpen(false);
  };

  const handleEditProduct = () => {
    if (!editingProduct) return;

    setProducts(
      products.map((p) => (p.id === editingProduct.id ? editingProduct : p)),
    );
    setEditingProduct(null);
    setIsEditDialogOpen(false);
  };

  const handleDeleteProduct = (productId: string) => {
    setProducts(products.filter((p) => p.id !== productId));
  };

  // Extract unique categories
  const categories = [
    language === "english" ? "all" : "الكل",
    ...new Set(products.map((product) => product.category)),
  ];

  const getCategoryDisplayName = (category: string) => {
    if (category === "all" || category === "الكل")
      return language === "english" ? "All" : "الكل";
    return category;
  };

  // Filter products based on search query and selected category
  const filteredProducts = products.filter((product) => {
    const matchesSearch = product.name
      .toLowerCase()
      .includes(searchQuery.toLowerCase());
    const matchesCategory =
      selectedCategory === "all" ||
      selectedCategory === "الكل" ||
      product.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  return (
    <div className="flex flex-col h-full bg-white rounded-md shadow-sm">
      <div className="p-4 border-b">
        <div className="flex items-center gap-2 mb-4">
          <div className="relative flex-1">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="البحث عن المنتجات..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-8"
            />
          </div>
          {showInventoryControls && (
            <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
              <DialogTrigger asChild>
                <Button className="flex items-center gap-2">
                  <Plus className="h-4 w-4" />
                  إضافة منتج جديد
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>إضافة منتج جديد</DialogTitle>
                </DialogHeader>
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="product-name">اسم المنتج</Label>
                    <Input
                      id="product-name"
                      value={newProduct.name}
                      onChange={(e) =>
                        setNewProduct({ ...newProduct, name: e.target.value })
                      }
                      placeholder="أدخل اسم المنتج"
                    />
                  </div>
                  <div>
                    <Label htmlFor="product-price">السعر (د.ع)</Label>
                    <Input
                      id="product-price"
                      type="number"
                      value={newProduct.price}
                      onChange={(e) =>
                        setNewProduct({
                          ...newProduct,
                          price: parseFloat(e.target.value) || 0,
                        })
                      }
                      placeholder="أدخل السعر"
                    />
                  </div>
                  <div>
                    <Label htmlFor="product-category">الفئة</Label>
                    <Select
                      value={newProduct.category}
                      onValueChange={(value) =>
                        setNewProduct({ ...newProduct, category: value })
                      }
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="اختر الفئة" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="برجر">برجر</SelectItem>
                        <SelectItem value="مقبلات">مقبلات</SelectItem>
                        <SelectItem value="سلطات">سلطات</SelectItem>
                        <SelectItem value="مشروبات">مشروبات</SelectItem>
                        <SelectItem value="بيتزا">بيتزا</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="product-description">الوصف</Label>
                    <Input
                      id="product-description"
                      value={newProduct.description}
                      onChange={(e) =>
                        setNewProduct({
                          ...newProduct,
                          description: e.target.value,
                        })
                      }
                      placeholder="أدخل وصف المنتج"
                    />
                  </div>
                  <div>
                    <Label htmlFor="product-image">رابط الصورة</Label>
                    <Input
                      id="product-image"
                      value={newProduct.image}
                      onChange={(e) =>
                        setNewProduct({ ...newProduct, image: e.target.value })
                      }
                      placeholder="أدخل رابط الصورة (اختياري)"
                    />
                  </div>
                  <div className="flex gap-2">
                    <Button onClick={handleAddProduct} className="flex-1">
                      إضافة المنتج
                    </Button>
                    <Button
                      variant="outline"
                      onClick={() => setIsAddDialogOpen(false)}
                      className="flex-1"
                    >
                      إلغاء
                    </Button>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          )}
        </div>

        <Tabs
          defaultValue={language === "english" ? "all" : "الكل"}
          value={selectedCategory}
          onValueChange={setSelectedCategory}
        >
          <TabsList className="w-full overflow-x-auto flex-wrap">
            {categories.map((category) => (
              <TabsTrigger
                key={category}
                value={category}
                className="capitalize"
              >
                {getCategoryDisplayName(category)}
              </TabsTrigger>
            ))}
          </TabsList>
        </Tabs>
      </div>

      <div className="flex-1 overflow-y-auto p-4">
        <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
          {filteredProducts.map((product) => (
            <Card key={product.id} className="overflow-hidden">
              <div className="relative h-32 overflow-hidden">
                <img
                  src={product.image}
                  alt={product.name}
                  className="w-full h-full object-cover"
                />
                <Badge className="absolute top-2 right-2">
                  {product.price.toFixed(2)} د.ع
                </Badge>
              </div>
              <CardContent className="p-3">
                <h3 className="font-medium text-sm mb-1 truncate">
                  {product.name}
                </h3>
                <p className="text-xs text-muted-foreground line-clamp-2 h-8">
                  {product.description || `${product.category} item`}
                </p>
                <div className="mt-2 flex justify-between items-center">
                  {showInventoryControls ? (
                    <div className="flex gap-1 w-full">
                      <Button
                        size="sm"
                        variant="outline"
                        className="flex-1"
                        onClick={() => {
                          setEditingProduct(product);
                          setIsEditDialogOpen(true);
                        }}
                      >
                        <Edit className="h-3 w-3 mr-1" />
                        تعديل
                      </Button>
                      <AlertDialog>
                        <AlertDialogTrigger asChild>
                          <Button
                            size="sm"
                            variant="destructive"
                            className="flex-1"
                          >
                            <Trash2 className="h-3 w-3 mr-1" />
                            حذف
                          </Button>
                        </AlertDialogTrigger>
                        <AlertDialogContent>
                          <AlertDialogHeader>
                            <AlertDialogTitle>تأكيد الحذف</AlertDialogTitle>
                            <AlertDialogDescription>
                              هل أنت متأكد من حذف {product.name}؟ لا يمكن
                              التراجع عن هذا الإجراء.
                            </AlertDialogDescription>
                          </AlertDialogHeader>
                          <AlertDialogFooter>
                            <AlertDialogCancel>إلغاء</AlertDialogCancel>
                            <AlertDialogAction
                              onClick={() => handleDeleteProduct(product.id)}
                            >
                              حذف
                            </AlertDialogAction>
                          </AlertDialogFooter>
                        </AlertDialogContent>
                      </AlertDialog>
                    </div>
                  ) : (
                    <Button
                      onClick={() => onAddToOrder(product)}
                      size="sm"
                      className="w-full"
                    >
                      إضافة للطلب
                    </Button>
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {filteredProducts.length === 0 && (
          <div className="flex flex-col items-center justify-center h-40 text-muted-foreground">
            <p>لم يتم العثور على منتجات</p>
            <Button
              variant="link"
              onClick={() => {
                setSearchQuery("");
                setSelectedCategory("الكل");
              }}
            >
              مسح المرشحات
            </Button>
          </div>
        )}
      </div>

      {/* Edit Product Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>تعديل المنتج</DialogTitle>
          </DialogHeader>
          {editingProduct && (
            <div className="space-y-4">
              <div>
                <Label htmlFor="edit-product-name">اسم المنتج</Label>
                <Input
                  id="edit-product-name"
                  value={editingProduct.name}
                  onChange={(e) =>
                    setEditingProduct({
                      ...editingProduct,
                      name: e.target.value,
                    })
                  }
                  placeholder="أدخل اسم المنتج"
                />
              </div>
              <div>
                <Label htmlFor="edit-product-price">السعر (د.ع)</Label>
                <Input
                  id="edit-product-price"
                  type="number"
                  value={editingProduct.price}
                  onChange={(e) =>
                    setEditingProduct({
                      ...editingProduct,
                      price: parseFloat(e.target.value) || 0,
                    })
                  }
                  placeholder="أدخل السعر"
                />
              </div>
              <div>
                <Label htmlFor="edit-product-category">الفئة</Label>
                <Select
                  value={editingProduct.category}
                  onValueChange={(value) =>
                    setEditingProduct({ ...editingProduct, category: value })
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="اختر الفئة" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="برجر">برجر</SelectItem>
                    <SelectItem value="مقبلات">مقبلات</SelectItem>
                    <SelectItem value="سلطات">سلطات</SelectItem>
                    <SelectItem value="مشروبات">مشروبات</SelectItem>
                    <SelectItem value="بيتزا">بيتزا</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="edit-product-description">الوصف</Label>
                <Input
                  id="edit-product-description"
                  value={editingProduct.description || ""}
                  onChange={(e) =>
                    setEditingProduct({
                      ...editingProduct,
                      description: e.target.value,
                    })
                  }
                  placeholder="أدخل وصف المنتج"
                />
              </div>
              <div>
                <Label htmlFor="edit-product-image">رابط الصورة</Label>
                <Input
                  id="edit-product-image"
                  value={editingProduct.image}
                  onChange={(e) =>
                    setEditingProduct({
                      ...editingProduct,
                      image: e.target.value,
                    })
                  }
                  placeholder="أدخل رابط الصورة"
                />
              </div>
              <div className="flex gap-2">
                <Button onClick={handleEditProduct} className="flex-1">
                  حفظ التغييرات
                </Button>
                <Button
                  variant="outline"
                  onClick={() => setIsEditDialogOpen(false)}
                  className="flex-1"
                >
                  إلغاء
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default ProductCatalog;
