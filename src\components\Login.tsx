import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>it<PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { LogIn, User, Lock } from "lucide-react";

interface LoginProps {
  onLogin: (userType: "admin" | "cashier") => void;
  language?: "english" | "arabic";
}

const Login = ({ onLogin, language = "arabic" }: LoginProps) => {
  const [username, setUsername] = useState("");
  const [password, setPassword] = useState("");
  const [error, setError] = useState("");
  const [isLoading, setIsLoading] = useState(false);

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError("");

    // Simple authentication logic
    if (username === "1" && password === "1") {
      localStorage.setItem("userType", "admin");
      localStorage.setItem("isLoggedIn", "true");
      onLogin("admin");
    } else if (username === "2" && password === "2") {
      localStorage.setItem("userType", "cashier");
      localStorage.setItem("isLoggedIn", "true");
      onLogin("cashier");
    } else {
      setError(
        language === "english"
          ? "Invalid username or password"
          : "اسم المستخدم أو كلمة المرور غير صحيحة",
      );
    }

    setIsLoading(false);
  };

  return (
    <div className="min-h-screen bg-background flex items-center justify-center p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4 w-12 h-12 bg-primary rounded-full flex items-center justify-center">
            <LogIn className="h-6 w-6 text-primary-foreground" />
          </div>
          <CardTitle className="text-2xl font-bold">
            {language === "english"
              ? "Restaurant POS Login"
              : "تسجيل دخول نظام المطعم"}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleLogin} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="username">
                {language === "english" ? "Username" : "اسم المستخدم"}
              </Label>
              <div className="relative">
                <User className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                <Input
                  id="username"
                  type="text"
                  value={username}
                  onChange={(e) => setUsername(e.target.value)}
                  placeholder={
                    language === "english"
                      ? "Enter username"
                      : "أدخل اسم المستخدم"
                  }
                  className="pl-10"
                  required
                />
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="password">
                {language === "english" ? "Password" : "كلمة المرور"}
              </Label>
              <div className="relative">
                <Lock className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                <Input
                  id="password"
                  type="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  placeholder={
                    language === "english"
                      ? "Enter password"
                      : "أدخل كلمة المرور"
                  }
                  className="pl-10"
                  required
                />
              </div>
            </div>
            {error && (
              <div className="text-red-500 text-sm text-center">{error}</div>
            )}
            <Button type="submit" className="w-full" disabled={isLoading}>
              {isLoading
                ? language === "english"
                  ? "Logging in..."
                  : "جاري تسجيل الدخول..."
                : language === "english"
                  ? "Login"
                  : "تسجيل الدخول"}
            </Button>
          </form>
          <div className="mt-6 text-center text-sm text-muted-foreground">
            <div className="space-y-1">
              <div>{language === "english" ? "Admin:" : "المدير:"} 1 / 1</div>
              <div>
                {language === "english" ? "Cashier:" : "الكاشير:"} 2 / 2
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default Login;
