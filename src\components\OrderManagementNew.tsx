import React, { useState, useEffect } from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Plus,
  Minus,
  ShoppingCart,
  Clock,
  CheckCircle,
  XCircle,
  Edit,
  Trash2,
  Phone,
  MapPin,
  User,
  <PERSON>f<PERSON><PERSON><PERSON>,
  Printer,
} from "lucide-react";
import { format } from "date-fns";
// import { db, Order, MenuItem, Customer, OrderItem } from "@/lib/database";
// import InvoicePrint from "./InvoicePrint";

interface OrderManagementProps {
  language?: "english" | "arabic";
  userType?: "admin" | "cashier";
}

const OrderManagement = ({ language = "arabic", userType = "cashier" }: OrderManagementProps) => {
  const [orders, setOrders] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);

  // Sample orders for display
  const sampleOrders = [
    {
      id: "1",
      orderNumber: "ORD001",
      customerName: "أحمد محمد",
      customerPhone: "0501234567",
      orderType: "dine-in",
      paymentMethod: "cash",
      status: "pending",
      total: 125.50,
      tableNumber: 5,
      createdAt: new Date(),
      items: [
        { name: "كبسة دجاج", quantity: 2, price: 45 },
        { name: "عصير برتقال", quantity: 2, price: 12 }
      ]
    },
    {
      id: "2",
      orderNumber: "ORD002",
      customerName: "فاطمة علي",
      customerPhone: "0507654321",
      orderType: "delivery",
      paymentMethod: "card",
      status: "preparing",
      total: 89.75,
      deliveryAddress: "شارع الملك فهد، الرياض",
      createdAt: new Date(),
      items: [
        { name: "مندي لحم", quantity: 1, price: 65 },
        { name: "شاي أحمر", quantity: 2, price: 8 }
      ]
    }
  ];

  const getStatusBadge = (status: string) => {
    const statusLabels: { [key: string]: string } = {
      pending: "في الانتظار",
      preparing: "قيد التحضير",
      ready: "جاهز",
      delivered: "تم التسليم",
      cancelled: "ملغي"
    };

    return (
      <span className={`px-2 py-1 rounded-full text-xs font-medium ${
        status === "pending" ? "bg-yellow-100 text-yellow-800" :
        status === "preparing" ? "bg-blue-100 text-blue-800" :
        status === "ready" ? "bg-green-100 text-green-800" :
        status === "delivered" ? "bg-gray-100 text-gray-800" :
        "bg-red-100 text-red-800"
      }`}>
        {statusLabels[status] || status}
      </span>
    );
  };

  const updateOrderStatus = (orderId: string, newStatus: string) => {
    alert(`تم تحديث حالة الطلب ${orderId} إلى: ${getStatusBadge(newStatus).props.children}`);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <RefreshCw className="h-8 w-8 animate-spin" />
        <span className="mr-2">جاري تحميل البيانات...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">إدارة الطلبات</h2>
        <div className="flex gap-2">
          <Button variant="outline">
            <RefreshCw className="h-4 w-4 mr-2" />
            تحديث
          </Button>
          <Button onClick={() => alert("سيتم إضافة نظام إنشاء الطلبات قريباً")}>
            <Plus className="h-4 w-4 mr-2" />
            طلب جديد
          </Button>
        </div>
      </div>

      {/* Sample Orders */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {sampleOrders.map(order => (
          <Card key={order.id} className="fade-in">
            <CardHeader className="pb-3">
              <div className="flex justify-between items-start">
                <div>
                  <CardTitle className="text-lg">{order.orderNumber}</CardTitle>
                  <p className="text-sm text-muted-foreground">
                    {format(order.createdAt, "dd/MM/yyyy HH:mm")}
                  </p>
                </div>
                {getStatusBadge(order.status)}
              </div>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex items-center gap-2">
                <User className="h-4 w-4" />
                <span className="text-sm">{order.customerName}</span>
              </div>

              <div className="flex items-center gap-2">
                <Phone className="h-4 w-4" />
                <span className="text-sm">{order.customerPhone}</span>
              </div>

              <div className="flex items-center gap-2">
                <span className={`px-2 py-1 rounded text-xs ${
                  order.orderType === "dine-in" ? "bg-green-100 text-green-800" :
                  order.orderType === "delivery" ? "bg-blue-100 text-blue-800" :
                  "bg-orange-100 text-orange-800"
                }`}>
                  {order.orderType === "dine-in" ? "تناول في المطعم" :
                   order.orderType === "delivery" ? "توصيل" : "سفري"}
                </span>
                {order.tableNumber && (
                  <span className="text-sm">طاولة {order.tableNumber}</span>
                )}
              </div>

              <div className="text-lg font-bold">
                {order.total.toFixed(2)} ر.س
              </div>

              <div className="flex flex-wrap gap-2">
                {order.status === "pending" && (
                  <Button
                    size="sm"
                    onClick={() => updateOrderStatus(order.id, "preparing")}
                  >
                    <Clock className="h-4 w-4 mr-1" />
                    بدء التحضير
                  </Button>
                )}
                {order.status === "preparing" && (
                  <Button
                    size="sm"
                    onClick={() => updateOrderStatus(order.id, "ready")}
                  >
                    <CheckCircle className="h-4 w-4 mr-1" />
                    جاهز
                  </Button>
                )}
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => alert("سيتم إضافة نظام الطباعة قريباً")}
                >
                  <Printer className="h-4 w-4 mr-1" />
                  طباعة
                </Button>
                <Button
                  size="sm"
                  variant="destructive"
                  onClick={() => updateOrderStatus(order.id, "cancelled")}
                >
                  <XCircle className="h-4 w-4 mr-1" />
                  إلغاء
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
};

export default OrderManagement;
