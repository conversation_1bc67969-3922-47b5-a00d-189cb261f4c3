import React, { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Plus,
  Minus,
  ShoppingCart,
  Clock,
  CheckCircle,
  XCircle,
  Edit,
  Trash2,
  Phone,
  MapPin,
  User,
  <PERSON>f<PERSON><PERSON><PERSON>,
  Printer,
} from "lucide-react";
import { format } from "date-fns";
import { db, Order, MenuItem, Customer, OrderItem } from "@/lib/database";
import { useAuth } from "@/contexts/AuthContext";
import InvoicePrint from "./InvoicePrint";

interface OrderManagementProps {
  language?: "english" | "arabic";
  userType?: "admin" | "cashier";
}

const OrderManagement = ({ language = "arabic", userType = "cashier" }: OrderManagementProps) => {
  const { user } = useAuth();
  const [orders, setOrders] = useState<Order[]>([]);
  const [menuItems, setMenuItems] = useState<MenuItem[]>([]);
  const [categories, setCategories] = useState<any[]>([]);
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [loading, setLoading] = useState(true);
  const [isNewOrderOpen, setIsNewOrderOpen] = useState(false);
  
  // New order form state
  const [newOrder, setNewOrder] = useState({
    customerName: "",
    customerPhone: "",
    orderType: "dine-in" as "dine-in" | "delivery" | "takeaway",
    paymentMethod: "cash" as "cash" | "card" | "online",
    tableNumber: "",
    deliveryAddress: "",
    notes: "",
    items: [] as OrderItem[],
  });

  const [selectedCategory, setSelectedCategory] = useState<string>("all");
  const [searchTerm, setSearchTerm] = useState("");
  const [printOrder, setPrintOrder] = useState<Order | null>(null);

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    setLoading(true);
    try {
      const [ordersData, menuData, categoriesData, customersData] = await Promise.all([
        db.getOrders(),
        db.getMenuItems(),
        db.getCategories(),
        db.getCustomers(),
      ]);
      
      setOrders(ordersData);
      setMenuItems(menuData.filter(item => item.available));
      setCategories(categoriesData.filter(cat => cat.active));
      setCustomers(customersData);
    } catch (error) {
      console.error("Error loading data:", error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusBadge = (status: Order["status"]) => {
    const statusConfig = {
      pending: { label: "في الانتظار", variant: "secondary" as const },
      preparing: { label: "قيد التحضير", variant: "default" as const },
      ready: { label: "جاهز", variant: "outline" as const },
      delivered: { label: "تم التسليم", variant: "outline" as const },
      cancelled: { label: "ملغي", variant: "destructive" as const },
    };
    
    const config = statusConfig[status];
    return <Badge variant={config.variant}>{config.label}</Badge>;
  };

  const updateOrderStatus = async (orderId: string, newStatus: Order["status"]) => {
    try {
      const updatedOrder = db.updateOrder(orderId, { 
        status: newStatus,
        completedAt: newStatus === "delivered" ? new Date() : undefined
      });
      
      if (updatedOrder) {
        setOrders(prev => prev.map(order => 
          order.id === orderId ? updatedOrder : order
        ));
      }
    } catch (error) {
      console.error("Error updating order status:", error);
    }
  };

  const addItemToOrder = (menuItem: MenuItem) => {
    const existingItem = newOrder.items.find(item => item.menuItemId === menuItem.id);
    
    if (existingItem) {
      setNewOrder(prev => ({
        ...prev,
        items: prev.items.map(item =>
          item.menuItemId === menuItem.id
            ? { ...item, quantity: item.quantity + 1, totalPrice: (item.quantity + 1) * item.unitPrice }
            : item
        )
      }));
    } else {
      const newItem: OrderItem = {
        id: `item_${Date.now()}`,
        menuItemId: menuItem.id,
        quantity: 1,
        unitPrice: menuItem.price,
        totalPrice: menuItem.price,
        notes: "",
      };
      
      setNewOrder(prev => ({
        ...prev,
        items: [...prev.items, newItem]
      }));
    }
  };

  const removeItemFromOrder = (itemId: string) => {
    setNewOrder(prev => ({
      ...prev,
      items: prev.items.filter(item => item.id !== itemId)
    }));
  };

  const updateItemQuantity = (itemId: string, quantity: number) => {
    if (quantity <= 0) {
      removeItemFromOrder(itemId);
      return;
    }
    
    setNewOrder(prev => ({
      ...prev,
      items: prev.items.map(item =>
        item.id === itemId
          ? { ...item, quantity, totalPrice: quantity * item.unitPrice }
          : item
      )
    }));
  };

  const calculateOrderTotal = () => {
    const subtotal = newOrder.items.reduce((sum, item) => sum + item.totalPrice, 0);
    const tax = subtotal * 0.15; // 15% tax
    const total = subtotal + tax;
    
    return { subtotal, tax, total };
  };

  const submitOrder = async () => {
    if (newOrder.items.length === 0) {
      alert("يرجى إضافة عناصر للطلب");
      return;
    }
    
    if (!newOrder.customerName.trim()) {
      alert("يرجى إدخال اسم العميل");
      return;
    }

    try {
      const { subtotal, tax, total } = calculateOrderTotal();
      
      const orderData = {
        customerId: undefined,
        customerName: newOrder.customerName,
        customerPhone: newOrder.customerPhone,
        items: newOrder.items,
        subtotal,
        tax,
        discount: 0,
        total,
        orderType: newOrder.orderType,
        paymentMethod: newOrder.paymentMethod,
        status: "pending" as const,
        tableNumber: newOrder.orderType === "dine-in" ? parseInt(newOrder.tableNumber) || undefined : undefined,
        deliveryAddress: newOrder.orderType === "delivery" ? newOrder.deliveryAddress : undefined,
        notes: newOrder.notes,
        createdBy: user?.id || "unknown",
      };

      const savedOrder = db.saveOrder(orderData);
      setOrders(prev => [savedOrder, ...prev]);
      
      // Reset form
      setNewOrder({
        customerName: "",
        customerPhone: "",
        orderType: "dine-in",
        paymentMethod: "cash",
        tableNumber: "",
        deliveryAddress: "",
        notes: "",
        items: [],
      });
      
      setIsNewOrderOpen(false);
      alert("تم إنشاء الطلب بنجاح!");
      
    } catch (error) {
      console.error("Error creating order:", error);
      alert("حدث خطأ أثناء إنشاء الطلب");
    }
  };

  const filteredMenuItems = menuItems.filter(item => {
    const matchesCategory = selectedCategory === "all" || item.category === selectedCategory;
    const matchesSearch = item.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         item.nameEn.toLowerCase().includes(searchTerm.toLowerCase());
    return matchesCategory && matchesSearch;
  });

  const filteredOrders = orders.filter(order => 
    order.status !== "delivered" && order.status !== "cancelled"
  );

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <RefreshCw className="h-8 w-8 animate-spin" />
        <span className="mr-2">جاري تحميل البيانات...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">إدارة الطلبات</h2>
        <div className="flex gap-2">
          <Button onClick={loadData} variant="outline">
            <RefreshCw className="h-4 w-4 mr-2" />
            تحديث
          </Button>
          <Dialog open={isNewOrderOpen} onOpenChange={setIsNewOrderOpen}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                طلب جديد
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
              <DialogHeader>
                <DialogTitle>إنشاء طلب جديد</DialogTitle>
              </DialogHeader>
              
              <Tabs defaultValue="customer" className="w-full">
                <TabsList className="grid w-full grid-cols-3">
                  <TabsTrigger value="customer">بيانات العميل</TabsTrigger>
                  <TabsTrigger value="items">العناصر</TabsTrigger>
                  <TabsTrigger value="summary">الملخص</TabsTrigger>
                </TabsList>
                
                <TabsContent value="customer" className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="customerName">اسم العميل *</Label>
                      <Input
                        id="customerName"
                        value={newOrder.customerName}
                        onChange={(e) => setNewOrder(prev => ({ ...prev, customerName: e.target.value }))}
                        placeholder="أدخل اسم العميل"
                      />
                    </div>
                    <div>
                      <Label htmlFor="customerPhone">رقم الهاتف</Label>
                      <Input
                        id="customerPhone"
                        value={newOrder.customerPhone}
                        onChange={(e) => setNewOrder(prev => ({ ...prev, customerPhone: e.target.value }))}
                        placeholder="أدخل رقم الهاتف"
                      />
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="orderType">نوع الطلب</Label>
                      <Select
                        value={newOrder.orderType}
                        onValueChange={(value: any) => setNewOrder(prev => ({ ...prev, orderType: value }))}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="dine-in">تناول في المطعم</SelectItem>
                          <SelectItem value="takeaway">سفري</SelectItem>
                          <SelectItem value="delivery">توصيل</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div>
                      <Label htmlFor="paymentMethod">طريقة الدفع</Label>
                      <Select
                        value={newOrder.paymentMethod}
                        onValueChange={(value: any) => setNewOrder(prev => ({ ...prev, paymentMethod: value }))}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="cash">نقدي</SelectItem>
                          <SelectItem value="card">بطاقة</SelectItem>
                          <SelectItem value="online">أونلاين</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                  
                  {newOrder.orderType === "dine-in" && (
                    <div>
                      <Label htmlFor="tableNumber">رقم الطاولة</Label>
                      <Input
                        id="tableNumber"
                        type="number"
                        value={newOrder.tableNumber}
                        onChange={(e) => setNewOrder(prev => ({ ...prev, tableNumber: e.target.value }))}
                        placeholder="أدخل رقم الطاولة"
                      />
                    </div>
                  )}
                  
                  {newOrder.orderType === "delivery" && (
                    <div>
                      <Label htmlFor="deliveryAddress">عنوان التوصيل</Label>
                      <Textarea
                        id="deliveryAddress"
                        value={newOrder.deliveryAddress}
                        onChange={(e) => setNewOrder(prev => ({ ...prev, deliveryAddress: e.target.value }))}
                        placeholder="أدخل عنوان التوصيل"
                      />
                    </div>
                  )}
                  
                  <div>
                    <Label htmlFor="notes">ملاحظات</Label>
                    <Textarea
                      id="notes"
                      value={newOrder.notes}
                      onChange={(e) => setNewOrder(prev => ({ ...prev, notes: e.target.value }))}
                      placeholder="ملاحظات إضافية"
                    />
                  </div>
                </TabsContent>
                
                <TabsContent value="items" className="space-y-4">
                  <div className="flex gap-4 mb-4">
                    <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                      <SelectTrigger className="w-48">
                        <SelectValue placeholder="اختر الفئة" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">جميع الفئات</SelectItem>
                        {categories.map(category => (
                          <SelectItem key={category.id} value={category.id}>
                            {category.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <Input
                      placeholder="البحث في القائمة..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="flex-1"
                    />
                  </div>
                  
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-4 max-h-96 overflow-y-auto">
                    {filteredMenuItems.map(item => (
                      <Card key={item.id} className="cursor-pointer hover:shadow-md transition-shadow">
                        <CardContent className="p-4">
                          <div className="space-y-2">
                            <h4 className="font-medium">{item.name}</h4>
                            <p className="text-sm text-muted-foreground">{item.description}</p>
                            <div className="flex justify-between items-center">
                              <span className="font-bold">{item.price.toFixed(2)} ر.س</span>
                              <Button
                                size="sm"
                                onClick={() => addItemToOrder(item)}
                              >
                                <Plus className="h-4 w-4" />
                              </Button>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </TabsContent>
                
                <TabsContent value="summary" className="space-y-4">
                  <div className="space-y-4">
                    <h4 className="font-medium">عناصر الطلب:</h4>
                    {newOrder.items.length === 0 ? (
                      <p className="text-muted-foreground">لم يتم إضافة عناصر بعد</p>
                    ) : (
                      <div className="space-y-2">
                        {newOrder.items.map(item => {
                          const menuItem = menuItems.find(mi => mi.id === item.menuItemId);
                          return (
                            <div key={item.id} className="flex justify-between items-center p-2 border rounded">
                              <div>
                                <span className="font-medium">{menuItem?.name}</span>
                                <span className="text-sm text-muted-foreground ml-2">
                                  {item.unitPrice.toFixed(2)} ر.س × {item.quantity}
                                </span>
                              </div>
                              <div className="flex items-center gap-2">
                                <Button
                                  size="sm"
                                  variant="outline"
                                  onClick={() => updateItemQuantity(item.id, item.quantity - 1)}
                                >
                                  <Minus className="h-4 w-4" />
                                </Button>
                                <span>{item.quantity}</span>
                                <Button
                                  size="sm"
                                  variant="outline"
                                  onClick={() => updateItemQuantity(item.id, item.quantity + 1)}
                                >
                                  <Plus className="h-4 w-4" />
                                </Button>
                                <Button
                                  size="sm"
                                  variant="destructive"
                                  onClick={() => removeItemFromOrder(item.id)}
                                >
                                  <Trash2 className="h-4 w-4" />
                                </Button>
                                <span className="font-medium">{item.totalPrice.toFixed(2)} ر.س</span>
                              </div>
                            </div>
                          );
                        })}
                      </div>
                    )}
                    
                    {newOrder.items.length > 0 && (
                      <div className="border-t pt-4 space-y-2">
                        <div className="flex justify-between">
                          <span>المجموع الفرعي:</span>
                          <span>{calculateOrderTotal().subtotal.toFixed(2)} ر.س</span>
                        </div>
                        <div className="flex justify-between">
                          <span>الضريبة (15%):</span>
                          <span>{calculateOrderTotal().tax.toFixed(2)} ر.س</span>
                        </div>
                        <div className="flex justify-between font-bold text-lg">
                          <span>المجموع الكلي:</span>
                          <span>{calculateOrderTotal().total.toFixed(2)} ر.س</span>
                        </div>
                      </div>
                    )}
                    
                    <Button 
                      onClick={submitOrder} 
                      className="w-full"
                      disabled={newOrder.items.length === 0}
                    >
                      إنشاء الطلب
                    </Button>
                  </div>
                </TabsContent>
              </Tabs>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Active Orders */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {filteredOrders.map(order => (
          <Card key={order.id} className="fade-in">
            <CardHeader className="pb-3">
              <div className="flex justify-between items-start">
                <div>
                  <CardTitle className="text-lg">{order.orderNumber}</CardTitle>
                  <p className="text-sm text-muted-foreground">
                    {format(order.createdAt, "dd/MM/yyyy HH:mm")}
                  </p>
                </div>
                {getStatusBadge(order.status)}
              </div>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex items-center gap-2">
                <User className="h-4 w-4" />
                <span className="text-sm">{order.customerName}</span>
              </div>
              
              {order.customerPhone && (
                <div className="flex items-center gap-2">
                  <Phone className="h-4 w-4" />
                  <span className="text-sm">{order.customerPhone}</span>
                </div>
              )}
              
              <div className="flex items-center gap-2">
                <span className={`px-2 py-1 rounded text-xs order-${order.orderType}`}>
                  {order.orderType === "dine-in" ? "تناول في المطعم" :
                   order.orderType === "delivery" ? "توصيل" : "سفري"}
                </span>
                {order.tableNumber && (
                  <span className="text-sm">طاولة {order.tableNumber}</span>
                )}
              </div>
              
              <div className="text-lg font-bold">
                {order.total.toFixed(2)} ر.س
              </div>
              
              <div className="flex flex-wrap gap-2">
                {order.status === "pending" && (
                  <Button
                    size="sm"
                    onClick={() => updateOrderStatus(order.id, "preparing")}
                  >
                    <Clock className="h-4 w-4 mr-1" />
                    بدء التحضير
                  </Button>
                )}
                {order.status === "preparing" && (
                  <Button
                    size="sm"
                    onClick={() => updateOrderStatus(order.id, "ready")}
                  >
                    <CheckCircle className="h-4 w-4 mr-1" />
                    جاهز
                  </Button>
                )}
                {order.status === "ready" && (
                  <Button
                    size="sm"
                    onClick={() => updateOrderStatus(order.id, "delivered")}
                  >
                    <CheckCircle className="h-4 w-4 mr-1" />
                    تم التسليم
                  </Button>
                )}
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => setPrintOrder(order)}
                >
                  <Printer className="h-4 w-4 mr-1" />
                  طباعة
                </Button>
                <Button
                  size="sm"
                  variant="destructive"
                  onClick={() => updateOrderStatus(order.id, "cancelled")}
                >
                  <XCircle className="h-4 w-4 mr-1" />
                  إلغاء
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
      
      {filteredOrders.length === 0 && (
        <Card>
          <CardContent className="text-center py-8">
            <ShoppingCart className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
            <p className="text-muted-foreground">لا توجد طلبات نشطة حالياً</p>
          </CardContent>
        </Card>
      )}

      {/* Print Invoice Modal */}
      {printOrder && (
        <InvoicePrint
          order={printOrder}
          onClose={() => setPrintOrder(null)}
        />
      )}
    </div>
  );
};

export default OrderManagement;
