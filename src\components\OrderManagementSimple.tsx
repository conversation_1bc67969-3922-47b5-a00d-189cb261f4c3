import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Plus,
  Clock,
  CheckCircle,
  XCircle,
  ShoppingCart,
  Phone,
  User,
  RefreshCw,
  Printer,
} from "lucide-react";
import { format } from "date-fns";

interface OrderManagementProps {
  language?: "english" | "arabic";
  userType?: "admin" | "cashier";
}

const OrderManagement = ({ language = "arabic", userType = "cashier" }: OrderManagementProps) => {
  const [isNewOrderOpen, setIsNewOrderOpen] = useState(false);
  const [newOrder, setNewOrder] = useState({
    customerName: "",
    customerPhone: "",
    orderType: "dine-in" as "dine-in" | "delivery" | "takeaway",
    paymentMethod: "cash" as "cash" | "card" | "online",
    tableNumber: "",
    deliveryAddress: "",
    notes: "",
    items: [] as any[],
  });

  // Sample menu items for ordering
  const sampleMenuItems = [
    { id: "1", name: "كبسة دجاج", price: 45, category: "أطباق رئيسية", description: "أرز بسمتي مع دجاج مشوي وخلطة البهارات الخاصة" },
    { id: "2", name: "مندي لحم", price: 65, category: "أطباق رئيسية", description: "لحم خروف طري مع أرز مندي مدخن" },
    { id: "3", name: "حمص بالطحينة", price: 18, category: "مقبلات", description: "حمص كريمي مع الطحينة وزيت الزيتون" },
    { id: "4", name: "فتوش", price: 22, category: "مقبلات", description: "سلطة خضار مشكلة مع الخبز المحمص" },
    { id: "5", name: "عصير برتقال طازج", price: 12, category: "مشروبات", description: "عصير برتقال طبيعي 100%" },
    { id: "6", name: "شاي أحمر", price: 8, category: "مشروبات", description: "شاي أحمر تقليدي مع السكر" },
    { id: "7", name: "كنافة نابلسية", price: 25, category: "حلويات", description: "كنافة محشوة بالجبن مع القطر" },
    { id: "8", name: "مهلبية", price: 15, category: "حلويات", description: "حلى كريمي بالحليب مع ماء الورد" }
  ];

  // Sample orders for display
  const sampleOrders = [
    {
      id: "1",
      orderNumber: "ORD001",
      customerName: "أحمد محمد",
      customerPhone: "0501234567",
      orderType: "dine-in",
      paymentMethod: "cash",
      status: "pending",
      total: 125.50,
      tableNumber: 5,
      createdAt: new Date(),
      items: [
        { name: "كبسة دجاج", quantity: 2, price: 45 },
        { name: "عصير برتقال", quantity: 2, price: 12 }
      ]
    },
    {
      id: "2", 
      orderNumber: "ORD002",
      customerName: "فاطمة علي",
      customerPhone: "0507654321",
      orderType: "delivery",
      paymentMethod: "card",
      status: "preparing",
      total: 89.75,
      deliveryAddress: "شارع الملك فهد، الرياض",
      createdAt: new Date(),
      items: [
        { name: "مندي لحم", quantity: 1, price: 65 },
        { name: "شاي أحمر", quantity: 2, price: 8 }
      ]
    },
    {
      id: "3",
      orderNumber: "ORD003", 
      customerName: "محمد سالم",
      customerPhone: "0509876543",
      orderType: "takeaway",
      paymentMethod: "cash",
      status: "ready",
      total: 65.00,
      createdAt: new Date(),
      items: [
        { name: "حمص بالطحينة", quantity: 1, price: 18 },
        { name: "فتوش", quantity: 1, price: 22 },
        { name: "كنافة نابلسية", quantity: 1, price: 25 }
      ]
    }
  ];

  const getStatusBadge = (status: string) => {
    const statusLabels: { [key: string]: string } = {
      pending: "في الانتظار",
      preparing: "قيد التحضير", 
      ready: "جاهز",
      delivered: "تم التسليم",
      cancelled: "ملغي"
    };
    
    return (
      <Badge variant={
        status === "pending" ? "secondary" :
        status === "preparing" ? "default" :
        status === "ready" ? "outline" :
        status === "delivered" ? "outline" :
        "destructive"
      }>
        {statusLabels[status] || status}
      </Badge>
    );
  };

  const updateOrderStatus = (orderId: string, newStatus: string) => {
    alert(`تم تحديث حالة الطلب ${orderId} إلى: ${newStatus}`);
  };

  const addItemToOrder = (menuItem: any) => {
    const existingItem = newOrder.items.find(item => item.id === menuItem.id);
    
    if (existingItem) {
      setNewOrder(prev => ({
        ...prev,
        items: prev.items.map(item =>
          item.id === menuItem.id
            ? { ...item, quantity: item.quantity + 1, totalPrice: (item.quantity + 1) * item.price }
            : item
        )
      }));
    } else {
      const newItem = {
        id: menuItem.id,
        name: menuItem.name,
        quantity: 1,
        price: menuItem.price,
        totalPrice: menuItem.price,
      };
      
      setNewOrder(prev => ({
        ...prev,
        items: [...prev.items, newItem]
      }));
    }
  };

  const removeItemFromOrder = (itemId: string) => {
    setNewOrder(prev => ({
      ...prev,
      items: prev.items.filter(item => item.id !== itemId)
    }));
  };

  const updateItemQuantity = (itemId: string, quantity: number) => {
    if (quantity <= 0) {
      removeItemFromOrder(itemId);
      return;
    }
    
    setNewOrder(prev => ({
      ...prev,
      items: prev.items.map(item =>
        item.id === itemId
          ? { ...item, quantity, totalPrice: quantity * item.price }
          : item
      )
    }));
  };

  const calculateOrderTotal = () => {
    const subtotal = newOrder.items.reduce((sum, item) => sum + item.totalPrice, 0);
    const tax = subtotal * 0.15; // 15% tax
    const total = subtotal + tax;
    
    return { subtotal, tax, total };
  };

  const submitOrder = () => {
    if (newOrder.items.length === 0) {
      alert("يرجى إضافة عناصر للطلب");
      return;
    }
    
    if (!newOrder.customerName.trim()) {
      alert("يرجى إدخال اسم العميل");
      return;
    }

    const { total } = calculateOrderTotal();
    const orderNumber = `ORD${Date.now()}`;
    
    alert(`تم إنشاء الطلب ${orderNumber} بنجاح!\nالمجموع: ${total.toFixed(2)} ر.س`);
    
    // Reset form
    setNewOrder({
      customerName: "",
      customerPhone: "",
      orderType: "dine-in",
      paymentMethod: "cash",
      tableNumber: "",
      deliveryAddress: "",
      notes: "",
      items: [],
    });
    
    setIsNewOrderOpen(false);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">إدارة الطلبات</h2>
        <div className="flex gap-2">
          <Button variant="outline">
            <RefreshCw className="h-4 w-4 mr-2" />
            تحديث
          </Button>
          <Button onClick={() => setIsNewOrderOpen(true)}>
            <Plus className="h-4 w-4 mr-2" />
            طلب جديد
          </Button>
        </div>
      </div>

      {/* Sample Orders */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {sampleOrders.map(order => (
          <Card key={order.id} className="fade-in">
            <CardHeader className="pb-3">
              <div className="flex justify-between items-start">
                <div>
                  <CardTitle className="text-lg">{order.orderNumber}</CardTitle>
                  <p className="text-sm text-muted-foreground">
                    {format(order.createdAt, "dd/MM/yyyy HH:mm")}
                  </p>
                </div>
                {getStatusBadge(order.status)}
              </div>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex items-center gap-2">
                <User className="h-4 w-4" />
                <span className="text-sm">{order.customerName}</span>
              </div>
              
              <div className="flex items-center gap-2">
                <Phone className="h-4 w-4" />
                <span className="text-sm">{order.customerPhone}</span>
              </div>
              
              <div className="flex items-center gap-2">
                <span className={`px-2 py-1 rounded text-xs ${
                  order.orderType === "dine-in" ? "bg-green-100 text-green-800" :
                  order.orderType === "delivery" ? "bg-blue-100 text-blue-800" :
                  "bg-orange-100 text-orange-800"
                }`}>
                  {order.orderType === "dine-in" ? "تناول في المطعم" :
                   order.orderType === "delivery" ? "توصيل" : "سفري"}
                </span>
                {order.tableNumber && (
                  <span className="text-sm">طاولة {order.tableNumber}</span>
                )}
              </div>
              
              <div className="text-lg font-bold">
                {order.total.toFixed(2)} ر.س
              </div>
              
              <div className="flex flex-wrap gap-2">
                {order.status === "pending" && (
                  <Button
                    size="sm"
                    onClick={() => updateOrderStatus(order.id, "preparing")}
                  >
                    <Clock className="h-4 w-4 mr-1" />
                    بدء التحضير
                  </Button>
                )}
                {order.status === "preparing" && (
                  <Button
                    size="sm"
                    onClick={() => updateOrderStatus(order.id, "ready")}
                  >
                    <CheckCircle className="h-4 w-4 mr-1" />
                    جاهز
                  </Button>
                )}
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => alert("سيتم إضافة نظام الطباعة قريباً")}
                >
                  <Printer className="h-4 w-4 mr-1" />
                  طباعة
                </Button>
                <Button
                  size="sm"
                  variant="destructive"
                  onClick={() => updateOrderStatus(order.id, "cancelled")}
                >
                  <XCircle className="h-4 w-4 mr-1" />
                  إلغاء
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
};

export default OrderManagement;
