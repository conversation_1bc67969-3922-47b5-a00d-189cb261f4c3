import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Plus,
  Clock,
  CheckCircle,
  XCircle,
  ShoppingCart,
  Phone,
  User,
  RefreshCw,
  Printer,
} from "lucide-react";
import { format } from "date-fns";

interface OrderManagementProps {
  language?: "english" | "arabic";
  userType?: "admin" | "cashier";
}

const OrderManagement = ({ language = "arabic", userType = "cashier" }: OrderManagementProps) => {
  const [isNewOrderOpen, setIsNewOrderOpen] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState("جميع الفئات");
  const [newOrder, setNewOrder] = useState({
    customerName: "",
    customerPhone: "",
    orderType: "dine-in" as "dine-in" | "delivery" | "takeaway",
    paymentMethod: "cash" as "cash" | "card" | "online",
    tableNumber: "",
    deliveryAddress: "",
    notes: "",
    items: [] as any[],
  });

  // Complete menu items - 16 products
  const sampleMenuItems = [
    // أطباق رئيسية
    { id: "1", name: "كبسة دجاج", price: 45, category: "أطباق رئيسية", description: "أرز بسمتي مع دجاج مشوي وخلطة البهارات الخاصة" },
    { id: "2", name: "مندي لحم", price: 65, category: "أطباق رئيسية", description: "لحم خروف طري مع أرز مندي مدخن" },
    { id: "3", name: "مشاوي مشكلة", price: 85, category: "أطباق رئيسية", description: "تشكيلة من اللحوم المشوية مع الخضار" },
    { id: "4", name: "فراخ مشوية", price: 38, category: "أطباق رئيسية", description: "دجاج كامل مشوي مع البهارات والأعشاب" },

    // مقبلات
    { id: "5", name: "حمص بالطحينة", price: 18, category: "مقبلات", description: "حمص كريمي مع الطحينة وزيت الزيتون" },
    { id: "6", name: "فتوش", price: 22, category: "مقبلات", description: "سلطة خضار مشكلة مع الخبز المحمص" },
    { id: "7", name: "تبولة", price: 20, category: "مقبلات", description: "سلطة البقدونس مع الطماطم والبرغل" },
    { id: "8", name: "بابا غنوج", price: 16, category: "مقبلات", description: "باذنجان مشوي مع الطحينة والثوم" },

    // مشروبات
    { id: "9", name: "عصير برتقال طازج", price: 12, category: "مشروبات", description: "عصير برتقال طبيعي 100%" },
    { id: "10", name: "شاي أحمر", price: 8, category: "مشروبات", description: "شاي أحمر تقليدي مع السكر" },
    { id: "11", name: "قهوة عربية", price: 10, category: "مشروبات", description: "قهوة عربية أصيلة مع الهيل" },
    { id: "12", name: "عصير مانجو", price: 15, category: "مشروبات", description: "عصير مانجو طازج ومنعش" },

    // حلويات
    { id: "13", name: "كنافة نابلسية", price: 25, category: "حلويات", description: "كنافة محشوة بالجبن مع القطر" },
    { id: "14", name: "مهلبية", price: 15, category: "حلويات", description: "حلى كريمي بالحليب مع ماء الورد" },
    { id: "15", name: "بقلاوة", price: 20, category: "حلويات", description: "حلوى شرقية بالعجين الرقيق والمكسرات" },
    { id: "16", name: "أم علي", price: 18, category: "حلويات", description: "حلى ساخن بالحليب والمكسرات والزبيب" }
  ];

  // Available tables
  const availableTables = [
    { id: 1, name: "طاولة 1", seats: 4, status: "available" },
    { id: 2, name: "طاولة 2", seats: 6, status: "occupied" },
    { id: 3, name: "طاولة 3", seats: 2, status: "available" },
    { id: 4, name: "طاولة 4", seats: 8, status: "available" },
    { id: 5, name: "طاولة 5", seats: 4, status: "reserved" },
    { id: 6, name: "طاولة 6", seats: 6, status: "available" },
    { id: 7, name: "طاولة 7", seats: 4, status: "occupied" },
    { id: 8, name: "طاولة 8", seats: 2, status: "available" },
    { id: 9, name: "طاولة 9", seats: 10, status: "available" },
    { id: 10, name: "طاولة 10", seats: 4, status: "available" }
  ];

  const categories = ["جميع الفئات", "أطباق رئيسية", "مقبلات", "مشروبات", "حلويات"];

  // Sample orders for display - more comprehensive
  const sampleOrders = [
    {
      id: "1",
      orderNumber: "ORD001",
      customerName: "أحمد محمد",
      customerPhone: "0501234567",
      orderType: "dine-in",
      paymentMethod: "cash",
      status: "pending",
      total: 125.50,
      tableNumber: 2,
      createdAt: new Date(),
      items: [
        { name: "كبسة دجاج", quantity: 2, price: 45 },
        { name: "عصير برتقال طازج", quantity: 2, price: 12 },
        { name: "مهلبية", quantity: 1, price: 15 }
      ]
    },
    {
      id: "2",
      orderNumber: "ORD002",
      customerName: "فاطمة علي",
      customerPhone: "0507654321",
      orderType: "delivery",
      paymentMethod: "card",
      status: "preparing",
      total: 89.75,
      deliveryAddress: "شارع الملك فهد، الرياض",
      createdAt: new Date(),
      items: [
        { name: "مندي لحم", quantity: 1, price: 65 },
        { name: "شاي أحمر", quantity: 2, price: 8 },
        { name: "بابا غنوج", quantity: 1, price: 16 }
      ]
    },
    {
      id: "3",
      orderNumber: "ORD003",
      customerName: "محمد سالم",
      customerPhone: "0509876543",
      orderType: "takeaway",
      paymentMethod: "cash",
      status: "ready",
      total: 65.00,
      createdAt: new Date(),
      items: [
        { name: "حمص بالطحينة", quantity: 1, price: 18 },
        { name: "فتوش", quantity: 1, price: 22 },
        { name: "كنافة نابلسية", quantity: 1, price: 25 }
      ]
    },
    {
      id: "4",
      orderNumber: "ORD004",
      customerName: "سارة أحمد",
      customerPhone: "0551234567",
      orderType: "dine-in",
      paymentMethod: "card",
      status: "delivered",
      total: 158.00,
      tableNumber: 7,
      createdAt: new Date(),
      items: [
        { name: "مشاوي مشكلة", quantity: 1, price: 85 },
        { name: "تبولة", quantity: 1, price: 20 },
        { name: "قهوة عربية", quantity: 2, price: 10 },
        { name: "بقلاوة", quantity: 1, price: 20 }
      ]
    },
    {
      id: "5",
      orderNumber: "ORD005",
      customerName: "خالد عبدالله",
      customerPhone: "0561234567",
      orderType: "dine-in",
      paymentMethod: "cash",
      status: "preparing",
      total: 76.00,
      tableNumber: 4,
      createdAt: new Date(),
      items: [
        { name: "فراخ مشوية", quantity: 2, price: 38 }
      ]
    },
    {
      id: "6",
      orderNumber: "ORD006",
      customerName: "نورا محمد",
      customerPhone: "0571234567",
      orderType: "delivery",
      paymentMethod: "online",
      status: "pending",
      total: 47.00,
      deliveryAddress: "حي النخيل، جدة",
      createdAt: new Date(),
      items: [
        { name: "عصير مانجو", quantity: 2, price: 15 },
        { name: "أم علي", quantity: 1, price: 18 }
      ]
    }
  ];

  const getStatusBadge = (status: string) => {
    const statusLabels: { [key: string]: string } = {
      pending: "في الانتظار",
      preparing: "قيد التحضير", 
      ready: "جاهز",
      delivered: "تم التسليم",
      cancelled: "ملغي"
    };
    
    return (
      <Badge variant={
        status === "pending" ? "secondary" :
        status === "preparing" ? "default" :
        status === "ready" ? "outline" :
        status === "delivered" ? "outline" :
        "destructive"
      }>
        {statusLabels[status] || status}
      </Badge>
    );
  };

  const updateOrderStatus = (orderId: string, newStatus: string) => {
    alert(`تم تحديث حالة الطلب ${orderId} إلى: ${newStatus}`);
  };

  const addItemToOrder = (menuItem: any) => {
    const existingItem = newOrder.items.find(item => item.id === menuItem.id);
    
    if (existingItem) {
      setNewOrder(prev => ({
        ...prev,
        items: prev.items.map(item =>
          item.id === menuItem.id
            ? { ...item, quantity: item.quantity + 1, totalPrice: (item.quantity + 1) * item.price }
            : item
        )
      }));
    } else {
      const newItem = {
        id: menuItem.id,
        name: menuItem.name,
        quantity: 1,
        price: menuItem.price,
        totalPrice: menuItem.price,
      };
      
      setNewOrder(prev => ({
        ...prev,
        items: [...prev.items, newItem]
      }));
    }
  };

  const removeItemFromOrder = (itemId: string) => {
    setNewOrder(prev => ({
      ...prev,
      items: prev.items.filter(item => item.id !== itemId)
    }));
  };

  const updateItemQuantity = (itemId: string, quantity: number) => {
    if (quantity <= 0) {
      removeItemFromOrder(itemId);
      return;
    }
    
    setNewOrder(prev => ({
      ...prev,
      items: prev.items.map(item =>
        item.id === itemId
          ? { ...item, quantity, totalPrice: quantity * item.price }
          : item
      )
    }));
  };

  const calculateOrderTotal = () => {
    const subtotal = newOrder.items.reduce((sum, item) => sum + item.totalPrice, 0);
    const tax = subtotal * 0.15; // 15% tax
    const total = subtotal + tax;
    
    return { subtotal, tax, total };
  };

  const submitOrder = () => {
    if (newOrder.items.length === 0) {
      alert("يرجى إضافة عناصر للطلب");
      return;
    }

    if (!newOrder.customerName.trim()) {
      alert("يرجى إدخال اسم العميل");
      return;
    }

    if (newOrder.orderType === "dine-in" && !newOrder.tableNumber) {
      alert("يرجى اختيار طاولة");
      return;
    }

    if (newOrder.orderType === "delivery" && !newOrder.deliveryAddress.trim()) {
      alert("يرجى إدخال عنوان التوصيل");
      return;
    }

    const { total } = calculateOrderTotal();
    const orderNumber = `ORD${Date.now()}`;

    alert(`تم إنشاء الطلب ${orderNumber} بنجاح!\nالمجموع: ${total.toFixed(2)} ر.س\nعدد العناصر: ${newOrder.items.length}`);

    // Reset form
    setNewOrder({
      customerName: "",
      customerPhone: "",
      orderType: "dine-in",
      paymentMethod: "cash",
      tableNumber: "",
      deliveryAddress: "",
      notes: "",
      items: [],
    });

    setSelectedCategory("جميع الفئات");
    setIsNewOrderOpen(false);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">إدارة الطلبات</h2>
        <div className="flex gap-2">
          <Button variant="outline">
            <RefreshCw className="h-4 w-4 mr-2" />
            تحديث
          </Button>
          <Button onClick={() => setIsNewOrderOpen(true)}>
            <Plus className="h-4 w-4 mr-2" />
            طلب جديد
          </Button>
        </div>
      </div>

      {/* Sample Orders */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {sampleOrders.map(order => (
          <Card key={order.id} className="fade-in">
            <CardHeader className="pb-3">
              <div className="flex justify-between items-start">
                <div>
                  <CardTitle className="text-lg">{order.orderNumber}</CardTitle>
                  <p className="text-sm text-muted-foreground">
                    {format(order.createdAt, "dd/MM/yyyy HH:mm")}
                  </p>
                </div>
                {getStatusBadge(order.status)}
              </div>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex items-center gap-2">
                <User className="h-4 w-4" />
                <span className="text-sm">{order.customerName}</span>
              </div>

              <div className="flex items-center gap-2">
                <Phone className="h-4 w-4" />
                <span className="text-sm">{order.customerPhone}</span>
              </div>

              <div className="flex items-center gap-2">
                <span className={`px-2 py-1 rounded text-xs ${
                  order.orderType === "dine-in" ? "bg-green-100 text-green-800" :
                  order.orderType === "delivery" ? "bg-blue-100 text-blue-800" :
                  "bg-orange-100 text-orange-800"
                }`}>
                  {order.orderType === "dine-in" ? "تناول في المطعم" :
                   order.orderType === "delivery" ? "توصيل" : "سفري"}
                </span>
                {order.tableNumber && (
                  <span className="text-sm">طاولة {order.tableNumber}</span>
                )}
              </div>

              <div className="text-lg font-bold">
                {order.total.toFixed(2)} ر.س
              </div>

              <div className="flex flex-wrap gap-2">
                {order.status === "pending" && (
                  <Button
                    size="sm"
                    onClick={() => updateOrderStatus(order.id, "preparing")}
                  >
                    <Clock className="h-4 w-4 mr-1" />
                    بدء التحضير
                  </Button>
                )}
                {order.status === "preparing" && (
                  <Button
                    size="sm"
                    onClick={() => updateOrderStatus(order.id, "ready")}
                  >
                    <CheckCircle className="h-4 w-4 mr-1" />
                    جاهز
                  </Button>
                )}
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => alert("سيتم إضافة نظام الطباعة قريباً")}
                >
                  <Printer className="h-4 w-4 mr-1" />
                  طباعة
                </Button>
                <Button
                  size="sm"
                  variant="destructive"
                  onClick={() => updateOrderStatus(order.id, "cancelled")}
                >
                  <XCircle className="h-4 w-4 mr-1" />
                  إلغاء
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* New Order Dialog */}
      {isNewOrderOpen && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-6xl w-full mx-4 max-h-[90vh] overflow-y-auto">
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-xl font-bold">إنشاء طلب جديد</h3>
              <Button variant="outline" onClick={() => setIsNewOrderOpen(false)}>
                ✕
              </Button>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {/* Customer Info */}
              <div className="space-y-4">
                <h4 className="font-semibold">بيانات العميل</h4>

                <div>
                  <label className="block text-sm font-medium mb-1">اسم العميل *</label>
                  <input
                    type="text"
                    value={newOrder.customerName}
                    onChange={(e) => setNewOrder(prev => ({ ...prev, customerName: e.target.value }))}
                    className="w-full p-2 border border-gray-300 rounded-md"
                    placeholder="أدخل اسم العميل"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-1">رقم الهاتف</label>
                  <input
                    type="text"
                    value={newOrder.customerPhone}
                    onChange={(e) => setNewOrder(prev => ({ ...prev, customerPhone: e.target.value }))}
                    className="w-full p-2 border border-gray-300 rounded-md"
                    placeholder="أدخل رقم الهاتف"
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium mb-1">نوع الطلب</label>
                    <select
                      value={newOrder.orderType}
                      onChange={(e) => setNewOrder(prev => ({ ...prev, orderType: e.target.value as any }))}
                      className="w-full p-2 border border-gray-300 rounded-md"
                    >
                      <option value="dine-in">تناول في المطعم</option>
                      <option value="takeaway">سفري</option>
                      <option value="delivery">توصيل</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium mb-1">طريقة الدفع</label>
                    <select
                      value={newOrder.paymentMethod}
                      onChange={(e) => setNewOrder(prev => ({ ...prev, paymentMethod: e.target.value as any }))}
                      className="w-full p-2 border border-gray-300 rounded-md"
                    >
                      <option value="cash">نقدي</option>
                      <option value="card">بطاقة</option>
                      <option value="online">أونلاين</option>
                    </select>
                  </div>
                </div>

                {newOrder.orderType === "dine-in" && (
                  <div>
                    <label className="block text-sm font-medium mb-1">اختر الطاولة</label>
                    <div className="grid grid-cols-2 gap-2 max-h-32 overflow-y-auto">
                      {availableTables.map(table => (
                        <button
                          key={table.id}
                          onClick={() => setNewOrder(prev => ({ ...prev, tableNumber: table.id.toString() }))}
                          className={`p-2 text-xs rounded border ${
                            newOrder.tableNumber === table.id.toString()
                              ? "bg-blue-500 text-white"
                              : table.status === "available"
                              ? "bg-green-100 text-green-800 hover:bg-green-200"
                              : table.status === "occupied"
                              ? "bg-red-100 text-red-800 cursor-not-allowed"
                              : "bg-yellow-100 text-yellow-800 cursor-not-allowed"
                          }`}
                          disabled={table.status !== "available"}
                        >
                          {table.name}
                          <br />
                          {table.seats} مقاعد
                          <br />
                          {table.status === "available" ? "متاحة" :
                           table.status === "occupied" ? "مشغولة" : "محجوزة"}
                        </button>
                      ))}
                    </div>
                  </div>
                )}

                {newOrder.orderType === "delivery" && (
                  <div>
                    <label className="block text-sm font-medium mb-1">عنوان التوصيل</label>
                    <textarea
                      value={newOrder.deliveryAddress}
                      onChange={(e) => setNewOrder(prev => ({ ...prev, deliveryAddress: e.target.value }))}
                      className="w-full p-2 border border-gray-300 rounded-md"
                      placeholder="أدخل عنوان التوصيل"
                      rows={3}
                    />
                  </div>
                )}

                <div>
                  <label className="block text-sm font-medium mb-1">ملاحظات</label>
                  <textarea
                    value={newOrder.notes}
                    onChange={(e) => setNewOrder(prev => ({ ...prev, notes: e.target.value }))}
                    className="w-full p-2 border border-gray-300 rounded-md"
                    placeholder="ملاحظات إضافية"
                    rows={3}
                  />
                </div>
              </div>

              {/* Menu Items */}
              <div className="space-y-4">
                <h4 className="font-semibold">القائمة (16 منتج)</h4>

                {/* Category Filter */}
                <div className="flex flex-wrap gap-2">
                  {categories.map(category => (
                    <button
                      key={category}
                      onClick={() => setSelectedCategory(category)}
                      className={`px-3 py-1 text-xs rounded ${
                        selectedCategory === category
                          ? "bg-blue-500 text-white"
                          : "bg-gray-100 text-gray-700 hover:bg-gray-200"
                      }`}
                    >
                      {category}
                    </button>
                  ))}
                </div>

                <div className="grid grid-cols-1 gap-2 max-h-96 overflow-y-auto">
                  {sampleMenuItems
                    .filter(item => selectedCategory === "جميع الفئات" || item.category === selectedCategory)
                    .map(item => (
                    <div key={item.id} className="border rounded-lg p-3 hover:shadow-md transition-shadow">
                      <h5 className="font-medium">{item.name}</h5>
                      <p className="text-xs text-gray-600 mb-2">{item.description}</p>
                      <div className="flex justify-between items-center">
                        <span className="font-bold text-green-600">{item.price} ر.س</span>
                        <Button
                          size="sm"
                          onClick={() => addItemToOrder(item)}
                          className="text-xs"
                        >
                          <Plus className="h-3 w-3 mr-1" />
                          إضافة
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Order Summary */}
              <div className="space-y-4">
                <h4 className="font-semibold">ملخص الطلب</h4>

                {newOrder.items.length > 0 ? (
                  <div className="space-y-2 max-h-64 overflow-y-auto">
                    {newOrder.items.map(item => (
                      <div key={item.id} className="flex justify-between items-center p-2 bg-gray-50 rounded">
                        <span className="text-sm">{item.name}</span>
                        <div className="flex items-center gap-2">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => updateItemQuantity(item.id, item.quantity - 1)}
                          >
                            <Minus className="h-3 w-3" />
                          </Button>
                          <span className="text-sm">{item.quantity}</span>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => updateItemQuantity(item.id, item.quantity + 1)}
                          >
                            <Plus className="h-3 w-3" />
                          </Button>
                          <Button
                            size="sm"
                            variant="destructive"
                            onClick={() => removeItemFromOrder(item.id)}
                          >
                            <Trash2 className="h-3 w-3" />
                          </Button>
                          <span className="font-medium text-sm">{item.totalPrice.toFixed(2)} ر.س</span>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-gray-500 text-center py-8">لم يتم إضافة أي عناصر بعد</p>
                )}

                {newOrder.items.length > 0 && (
                  <div className="space-y-2 border-t pt-4">
                    <div className="flex justify-between">
                      <span>المجموع الفرعي:</span>
                      <span>{calculateOrderTotal().subtotal.toFixed(2)} ر.س</span>
                    </div>
                    <div className="flex justify-between">
                      <span>الضريبة (15%):</span>
                      <span>{calculateOrderTotal().tax.toFixed(2)} ر.س</span>
                    </div>
                    <div className="flex justify-between font-bold text-lg">
                      <span>المجموع الكلي:</span>
                      <span>{calculateOrderTotal().total.toFixed(2)} ر.س</span>
                    </div>
                  </div>
                )}

                <Button
                  onClick={submitOrder}
                  className="w-full"
                  disabled={newOrder.items.length === 0}
                >
                  إنشاء الطلب ({newOrder.items.length} عنصر)
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default OrderManagement;
